# Create VS Mac Tools v2 using Automator

## Method 1: Automator Application (Recommended)

### Steps:
1. **Open Automator**
2. **Choose "Application"**
3. **Add "Run Shell Script" action**
4. **Set Shell to**: `/bin/bash`
5. **Paste this script**:

```bash
#!/bin/bash
cd "/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker"
/usr/bin/python3 vs_mac_tools_v2.py
```

6. **Save as**: "VS Mac Tools v2.app"
7. **Add custom icon**: Right-click app → Get Info → drag vsmactool.png to icon

### Advantages:
- ✅ **Fast and simple**
- ✅ **No bundling issues**
- ✅ **Native macOS app**
- ✅ **Full UI functionality**
- ✅ **Easy to update**

### Disadvantages:
- ❌ **Requires Python on target machine**
- ❌ **Requires dependencies installed**

## Method 2: Shell Script Wrapper

Create a shell script that launches the Python app:

```bash
#!/bin/bash
# VS Mac Tools v2 Launcher
cd "$(dirname "$0")"
/usr/bin/python3 vs_mac_tools_v2.py
```

Then use Platypus or create .app bundle manually.
