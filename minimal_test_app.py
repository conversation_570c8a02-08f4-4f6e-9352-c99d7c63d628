#!/usr/bin/env python3
"""
Minimal test app to isolate the blank UI issue
"""

import customtkinter as ctk
import os
import sys

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class MinimalApp(ctk.CTk):
    def __init__(self):
        try:
            print("Initializing MinimalApp...")
            super().__init__()
            
            # Basic configuration
            self.title("Minimal Test App")
            self.geometry("600x400")
            
            # Set appearance
            ctk.set_appearance_mode("dark")
            
            # Configure colors
            self.colors = {
                "window_bg": "#1E1E1E",
                "frame_bg": "#2A2A2A",
                "text_primary": "#FFFFFF",
                "accent_blue": "#007AFF"
            }
            
            self.configure(fg_color=self.colors["window_bg"])
            
            print("Creating UI elements...")
            self.create_ui()
            
            print("MinimalApp initialized successfully")
            
        except Exception as e:
            print(f"Error in MinimalApp.__init__: {e}")
            import traceback
            traceback.print_exc()
    
    def create_ui(self):
        """Create the user interface"""
        try:
            # Main frame
            self.main_frame = ctk.CTkFrame(
                self,
                fg_color=self.colors["frame_bg"],
                corner_radius=10
            )
            self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            # Title label
            self.title_label = ctk.CTkLabel(
                self.main_frame,
                text="Minimal Test Application",
                font=("Arial", 24, "bold"),
                text_color=self.colors["text_primary"]
            )
            self.title_label.pack(pady=20)
            
            # Test label
            self.test_label = ctk.CTkLabel(
                self.main_frame,
                text="If you can see this, the UI is working!",
                font=("Arial", 16),
                text_color=self.colors["text_primary"]
            )
            self.test_label.pack(pady=10)
            
            # Test button
            self.test_button = ctk.CTkButton(
                self.main_frame,
                text="Test Button",
                command=self.button_clicked,
                fg_color=self.colors["accent_blue"],
                font=("Arial", 14)
            )
            self.test_button.pack(pady=20)
            
            # Text box
            self.text_box = ctk.CTkTextbox(
                self.main_frame,
                width=400,
                height=100,
                font=("Arial", 12)
            )
            self.text_box.pack(pady=10)
            self.text_box.insert("1.0", "This is a test text box.\nIf you can see this text, the UI is rendering correctly.")
            
            print("UI elements created successfully")
            
        except Exception as e:
            print(f"Error creating UI: {e}")
            import traceback
            traceback.print_exc()
    
    def button_clicked(self):
        """Handle button click"""
        try:
            print("Button clicked!")
            self.text_box.insert("end", "\nButton was clicked!")
        except Exception as e:
            print(f"Error in button_clicked: {e}")

def main():
    """Main entry point"""
    try:
        print("Starting minimal test app...")
        
        app = MinimalApp()
        print("App created, starting mainloop...")
        
        app.mainloop()
        print("App closed")
        
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
