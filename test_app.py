#!/usr/bin/env python3

# Quick test to verify VS Mac Tools v2 is working

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

print("🧪 Testing VS Mac Tools v2...")

try:
    print("1. Testing imports...")
    import customtkinter as ctk
    print("   ✅ CustomTkinter imported")
    
    from PIL import Image
    print("   ✅ PIL imported")
    
    import keyring
    print("   ✅ Keyring imported")
    
    from password_dialog import get_sudo_password
    print("   ✅ Password dialog imported")
    
    print("\n2. Testing main app import...")
    import vs_mac_tools_v2
    print("   ✅ Main app imported")
    
    print("\n3. Testing app creation...")
    app = vs_mac_tools_v2.InternetCheckerApp()
    print("   ✅ App created successfully")
    
    print(f"   📱 App title: {app.title()}")
    print(f"   📐 App geometry: {app.geometry()}")
    
    print("\n4. Testing app update...")
    app.update()
    print("   ✅ App updated successfully")
    
    print("\n5. Closing app...")
    app.destroy()
    print("   ✅ App closed successfully")
    
    print("\n🎉 ALL TESTS PASSED!")
    print("   VS Mac Tools v2 is working perfectly!")
    print("\n🚀 To run the app:")
    print("   /usr/bin/python3 vs_mac_tools_v2.py")
    print("   or")
    print("   ./launch_vs_mac_tools.sh")
    print("   or")
    print("   open 'VS Mac Tools v2.app'")
    
except Exception as e:
    print(f"\n❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
    print("\n🔧 Try installing missing packages:")
    print("   /usr/bin/python3 -m pip install --user customtkinter pillow keyring")
