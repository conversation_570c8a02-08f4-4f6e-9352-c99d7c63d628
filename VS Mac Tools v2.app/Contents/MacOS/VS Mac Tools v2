#!/bin/bash

# VS Mac Tools v2 App Launcher with Virtual Environment
export TK_SILENCE_DEPRECATION=1

# Get the script directory
SCRIPT_DIR="/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker"

# Change to script directory
cd "$SCRIPT_DIR"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    osascript -e 'display alert "Setup Required" message "Virtual environment not found. Please run the setup first by executing launch_vs_mac_tools.sh"'
    exit 1
fi

# Activate virtual environment and launch
source venv/bin/activate
exec python vs_mac_tools_v2.py
