# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files

datas = [('img', 'img'), ('xml', 'xml'), ('password_dialog.py', '.')]
datas += collect_data_files('customtkinter')
datas += collect_data_files('tkinter')


a = Analysis(
    ['vs_mac_tools_v2.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=['customtkinter', 'tkinter', 'tkinter.filedialog', 'tkinter.messagebox', 'PIL', 'keyring'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='VS Mac Tools v2 Truly Standalone',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',
    codesign_identity=None,
    entitlements_file=None,
    icon=['img/vsmactool.icns'],
)
app = BUNDLE(
    exe,
    name='VS Mac Tools v2 Truly Standalone.app',
    icon='img/vsmactool.icns',
    bundle_identifier='com.vonzki.vsmactools',
)
