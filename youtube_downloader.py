import customtkinter as ctk
from tkinter import filedialog, messagebox
import threading
import os
import yt_dlp
import youtube_dl
import subprocess
import sys
import pyperclip
import re
import time
import socket

# Set CustomTkinter appearance
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

# Single instance check using socket
def check_single_instance():
    """Ensure only one instance of the app is running using socket binding"""
    try:
        # Try to bind to a specific port - if it fails, another instance is running
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(('127.0.0.1', 65432))  # Use a specific port for this app
        sock.listen(1)

        # If we get here, we successfully bound to the port
        return sock

    except socket.error:
        # Port is already in use - another instance is running
        print("YouTube Downloader is already running!")
        print("Please check your Dock or use Cmd+Tab to find the existing window.")
        sys.exit(0)

class YouTubeDownloader(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("VS YT Downloader v1.0.1")
        self.geometry("640x550")

        # Clipboard monitoring
        self.last_clipboard = ""
        self.clipboard_monitoring = True

        self.setup_ui()
        self.start_clipboard_monitoring()

    def setup_ui(self):
        # URL input
        url_frame = ctk.CTkFrame(self)
        url_frame.pack(fill="x", padx=20, pady=20)

        ctk.CTkLabel(url_frame, text="Video URL:").pack(side="left", padx=(10, 5), pady=10)
        self.url_var = ctk.StringVar()
        self.url_entry = ctk.CTkEntry(url_frame, textvariable=self.url_var, width=300)
        self.url_entry.pack(side="left", padx=(5, 5), pady=10, fill="x", expand=True)

        # Paste button
        self.paste_btn = ctk.CTkButton(url_frame, text="Paste", command=self.paste_from_clipboard, width=60)
        self.paste_btn.pack(side="left", padx=(5, 5), pady=10)

        # Clipboard monitoring toggle
        self.clipboard_var = ctk.BooleanVar(value=True)
        self.clipboard_checkbox = ctk.CTkCheckBox(url_frame, text="Auto-detect",
                                                variable=self.clipboard_var,
                                                command=self.toggle_clipboard_monitoring,
                                                width=80)
        self.clipboard_checkbox.pack(side="left", padx=(5, 10), pady=10)

        # Format selection
        format_frame = ctk.CTkFrame(self)
        format_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(format_frame, text="Format:").pack(side="left", padx=(10, 5), pady=10)
        self.format_var = ctk.StringVar(value="mp4")
        self.format_combo = ctk.CTkComboBox(format_frame, variable=self.format_var,
                                          values=["mp4", "mp3"], width=120)
        self.format_combo.pack(side="left", padx=(5, 10), pady=10)

        # Quality selection
        quality_frame = ctk.CTkFrame(self)
        quality_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(quality_frame, text="Quality:").pack(side="left", padx=(10, 5), pady=10)
        self.quality_var = ctk.StringVar(value="360p")
        self.quality_combo = ctk.CTkComboBox(quality_frame, variable=self.quality_var,
                                           values=["Highest", "720p", "480p", "360p"], width=120)
        self.quality_combo.pack(side="left", padx=(5, 10), pady=10)

        # Destination folder
        dest_frame = ctk.CTkFrame(self)
        dest_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(dest_frame, text="Save to:").pack(side="left", padx=(10, 5), pady=10)
        self.dest_var = ctk.StringVar(value=os.path.join(os.path.expanduser("~"), "Downloads"))
        self.dest_entry = ctk.CTkEntry(dest_frame, textvariable=self.dest_var, width=300)
        self.dest_entry.pack(side="left", padx=(5, 10), pady=10, fill="x", expand=True)
        self.browse_btn = ctk.CTkButton(dest_frame, text="Browse", command=self.browse_folder, width=80)
        self.browse_btn.pack(side="left", padx=(0, 10), pady=10)

        # Download button
        button_frame = ctk.CTkFrame(self)
        button_frame.pack(fill="x", padx=20, pady=20)

        # Create a sub-frame for buttons
        btn_container = ctk.CTkFrame(button_frame, fg_color="transparent")
        btn_container.pack(pady=15)

        self.test_btn = ctk.CTkButton(btn_container, text="Test URL",
                                    command=self.test_url, width=120, height=40)
        self.test_btn.pack(side="left", padx=(0, 10))

        self.download_btn = ctk.CTkButton(btn_container, text="Download",
                                        command=self.start_download, width=160, height=40)
        self.download_btn.pack(side="left", padx=(0, 5))

        # Update and Exit buttons
        self.update_btn = ctk.CTkButton(btn_container, text="Update",
                                      command=self.update_libraries, width=80, height=40)
        self.update_btn.pack(side="left", padx=(5, 5))

        self.exit_btn = ctk.CTkButton(btn_container, text="Exit",
                                    command=self.quit_app, width=100, height=40,
                                    fg_color="Indian red", hover_color="Red")
        self.exit_btn.pack(side="left", padx=(5, 0))

        # Progress bar
        progress_frame = ctk.CTkFrame(self)
        progress_frame.pack(fill="x", padx=20, pady=10)

        self.progress_var = ctk.DoubleVar()
        self.progress_bar = ctk.CTkProgressBar(progress_frame, variable=self.progress_var)
        self.progress_bar.pack(fill="x", padx=20, pady=(15, 5))

        self.status_var = ctk.StringVar(value="Ready")
        self.status_label = ctk.CTkLabel(progress_frame, textvariable=self.status_var)
        self.status_label.pack(pady=(5, 15))

        # Version info
        version_frame = ctk.CTkFrame(self)
        version_frame.pack(fill="x", padx=20, pady=(0, 10))

        self.version_var = ctk.StringVar(value="Loading version info...")
        self.version_label = ctk.CTkLabel(version_frame, textvariable=self.version_var,
                                        text_color="gray", font=("Arial", 10))
        self.version_label.pack(pady=5)

        # Load version info in background
        threading.Thread(target=self.load_version_info, daemon=True).start()

    def load_version_info(self):
        """Load version information for yt-dlp and youtube-dl"""
        try:
            # Get yt-dlp version
            try:
                result1 = subprocess.run([sys.executable, "-c", "import yt_dlp; print(yt_dlp.version.__version__)"],
                                       capture_output=True, text=True, timeout=5)
                yt_dlp_version = result1.stdout.strip() if result1.returncode == 0 else "Unknown"
            except:
                yt_dlp_version = "Unknown"

            # Get youtube-dl version
            try:
                result2 = subprocess.run([sys.executable, "-c", "import youtube_dl; print(youtube_dl.version.__version__)"],
                                       capture_output=True, text=True, timeout=5)
                youtube_dl_version = result2.stdout.strip() if result2.returncode == 0 else "Unknown"
            except:
                youtube_dl_version = "Unknown"

            version_text = f"yt-dlp: {yt_dlp_version} | youtube-dl: {youtube_dl_version}"
            self.version_var.set(version_text)

        except Exception:
            self.version_var.set("Version info unavailable")

    def paste_from_clipboard(self):
        """Manually paste from clipboard"""
        try:
            clipboard_content = pyperclip.paste()
            if clipboard_content and self.is_valid_youtube_url(clipboard_content):
                self.url_var.set(clipboard_content)
                self.status_var.set("URL pasted from clipboard!")
            elif clipboard_content:
                # Try to extract YouTube URL from the text
                extracted_url = self.extract_youtube_url(clipboard_content)
                if extracted_url:
                    self.url_var.set(extracted_url)
                    self.status_var.set("YouTube URL extracted from clipboard!")
                else:
                    self.url_var.set(clipboard_content)
                    self.status_var.set("Content pasted (please verify URL)")
            else:
                self.status_var.set("Clipboard is empty")
        except Exception as e:
            self.status_var.set(f"Error accessing clipboard: {str(e)}")

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.dest_var.set(folder)

    def toggle_clipboard_monitoring(self):
        """Toggle clipboard monitoring on/off"""
        self.clipboard_monitoring = self.clipboard_var.get()
        if self.clipboard_monitoring:
            self.start_clipboard_monitoring()

    def start_clipboard_monitoring(self):
        """Start monitoring clipboard for YouTube URLs"""
        if self.clipboard_monitoring:
            threading.Thread(target=self.monitor_clipboard, daemon=True).start()

    def monitor_clipboard(self):
        """Monitor clipboard for YouTube URLs and auto-fill"""
        while self.clipboard_monitoring:
            try:
                current_clipboard = pyperclip.paste()
                if (current_clipboard != self.last_clipboard and
                    current_clipboard and
                    self.is_valid_youtube_url(current_clipboard)):

                    self.last_clipboard = current_clipboard
                    # Update UI in main thread - always update if it's a valid YouTube URL
                    self.after(0, lambda url=current_clipboard: self.url_var.set(url))
                    self.after(0, lambda: self.status_var.set("YouTube URL detected from clipboard!"))

                time.sleep(1)  # Check every second
            except Exception:
                # Ignore clipboard errors and continue monitoring
                time.sleep(2)

    def extract_youtube_url(self, text):
        """Extract YouTube URL from text using regex"""
        youtube_regex = re.compile(
            r'(https?://)?(www\.)?(youtube|youtu|youtube-nocookie)\.(com|be)/'
            r'(watch\?v=|embed/|v/|.+\?v=)?([^&=%\?]{11})'
        )
        match = youtube_regex.search(text)
        if match:
            video_id = match.group(6)
            return f"https://www.youtube.com/watch?v={video_id}"
        return None

    def start_download(self):
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return

        # Basic URL validation
        if not self.is_valid_youtube_url(url):
            messagebox.showerror("Error", "Please enter a valid YouTube URL\n\nExamples:\n• https://www.youtube.com/watch?v=VIDEO_ID\n• https://youtu.be/VIDEO_ID")
            return

        self.download_btn.configure(state="disabled")
        self.status_var.set("Preparing download...")
        self.progress_var.set(0)

        # Start download in a separate thread
        threading.Thread(target=self.download_video, daemon=True).start()

    def is_valid_youtube_url(self, url):
        """Basic YouTube URL validation"""
        youtube_patterns = [
            'youtube.com/watch',
            'youtu.be/',
            'youtube.com/embed/',
            'youtube.com/v/',
            'm.youtube.com/watch'
        ]
        return any(pattern in url.lower() for pattern in youtube_patterns)

    def test_url(self):
        """Test if URL can be extracted without downloading"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return

        if not self.is_valid_youtube_url(url):
            messagebox.showerror("Error", "Please enter a valid YouTube URL")
            return

        self.test_btn.configure(state="disabled")
        self.status_var.set("Testing URL...")

        # Start test in a separate thread
        threading.Thread(target=self.test_url_extraction, daemon=True).start()

    def test_url_extraction(self):
        """Test URL extraction with both libraries"""
        url = self.url_var.get().strip()

        try:
            # Try youtube-dl first (like ClipGrab)
            self.status_var.set("Testing with youtube-dl...")
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': True,
                'no_check_certificate': True,
            }

            with youtube_dl.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                title = info.get('title', 'Unknown')
                duration = info.get('duration', 0)

                duration_str = f"{duration//60}:{duration%60:02d}" if duration else "Unknown"

                self.status_var.set(f"✅ URL is valid (youtube-dl)! Title: {title}")
                return

        except Exception as e1:
            try:
                # Try yt-dlp as fallback
                self.status_var.set("Testing with yt-dlp...")
                ydl_opts = {'quiet': True, 'no_warnings': True}

                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    info = ydl.extract_info(url, download=False)
                    title = info.get('title', 'Unknown')
                    duration = info.get('duration', 0)

                    duration_str = f"{duration//60}:{duration%60:02d}" if duration else "Unknown"

                    self.status_var.set(f"✅ URL is valid! Title: {title}")
                    return

            except Exception as e2:
                self.status_var.set("❌ URL test failed")
                messagebox.showerror("Test Failed",
                                   f"Both extraction methods failed:\n\n"
                                   f"youtube-dl error: {str(e1)[:100]}...\n\n"
                                   f"yt-dlp error: {str(e2)[:100]}...\n\n"
                                   f"This video may be:\n"
                                   f"• Private or deleted\n"
                                   f"• Age-restricted\n"
                                   f"• Geographically blocked\n"
                                   f"• Temporarily unavailable")

        finally:
            self.test_btn.configure(state="normal")

    def update_libraries(self):
        """Update yt-dlp and youtube-dl to latest versions"""
        self.update_btn.configure(state="disabled")
        self.status_var.set("Updating libraries...")

        # Start update in a separate thread
        threading.Thread(target=self.perform_update, daemon=True).start()

    def perform_update(self):
        """Perform the actual update"""
        try:
            self.status_var.set("Updating yt-dlp...")

            # Update yt-dlp
            result1 = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "yt-dlp"],
                                   capture_output=True, text=True)

            self.status_var.set("Updating youtube-dl...")

            # Update youtube-dl
            result2 = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "youtube-dl"],
                                   capture_output=True, text=True)

            if result1.returncode == 0 and result2.returncode == 0:
                self.status_var.set("✅ Libraries updated successfully!")
                messagebox.showinfo("Update Complete", "yt-dlp and youtube-dl have been updated to the latest versions!")
            else:
                error_msg = "Update failed:\n"
                if result1.returncode != 0:
                    error_msg += f"yt-dlp error: {result1.stderr}\n"
                if result2.returncode != 0:
                    error_msg += f"youtube-dl error: {result2.stderr}"

                self.status_var.set("❌ Update failed")
                messagebox.showerror("Update Failed", error_msg)

        except Exception as e:
            self.status_var.set("❌ Update error")
            messagebox.showerror("Update Error", f"Failed to update libraries: {str(e)}")

        finally:
            self.update_btn.configure(state="normal")

    def quit_app(self):
        """Exit the application"""
        self.quit()
        self.destroy()

    def try_subprocess_download(self, url, format_type, quality, destination):
        """Try downloading using subprocess (direct command line)"""
        try:
            self.status_var.set("Trying direct yt-dlp command...")

            # Try yt-dlp first with aggressive options
            cmd = ["yt-dlp"]

            # Output template
            cmd.extend(["-o", os.path.join(destination, "%(title)s.%(ext)s")])

            # Aggressive bypass options
            cmd.extend([
                "--no-check-certificate",
                "--ignore-errors",
                "--no-warnings",
                "--extractor-args", "youtube:player_client=android,web",
                "--extractor-args", "youtube:player_skip=webpage,configs",
                "--user-agent", "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36"
            ])

            # Format selection
            if format_type == "mp3":
                cmd.extend(["--extract-audio", "--audio-format", "mp3", "--audio-quality", "192K"])
            else:  # mp4
                if quality == "Highest":
                    cmd.extend(["-f", "best[ext=mp4]/best"])
                else:
                    height = quality.replace('p', '')
                    cmd.extend(["-f", f"best[height<={height}][ext=mp4]/best[height<={height}]/best"])

            cmd.append(url)

            # Run command
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=destination)

            if result.returncode == 0:
                return True
            else:
                self.status_var.set(f"yt-dlp subprocess failed, trying youtube-dl...")

                # Try youtube-dl as fallback
                cmd2 = ["youtube-dl"]
                cmd2.extend(["-o", os.path.join(destination, "%(title)s.%(ext)s")])
                cmd2.extend(["--no-check-certificate", "--ignore-errors"])

                if format_type == "mp3":
                    cmd2.extend(["--extract-audio", "--audio-format", "mp3", "--audio-quality", "192K"])
                else:
                    if quality == "Highest":
                        cmd2.extend(["-f", "best[ext=mp4]/best"])
                    else:
                        height = quality.replace('p', '')
                        cmd2.extend(["-f", f"best[height<={height}][ext=mp4]/best[height<={height}]/best"])

                cmd2.append(url)
                result2 = subprocess.run(cmd2, capture_output=True, text=True, cwd=destination)

                if result2.returncode == 0:
                    return True
                else:
                    self.status_var.set(f"Both subprocess methods failed...")
                    return False

        except Exception as e:
            self.status_var.set(f"Subprocess error: {str(e)[:50]}...")
            return False

    def try_gallery_dl_download(self, url, format_type, quality, destination):
        """Try downloading using gallery-dl as final fallback"""
        try:
            self.status_var.set("Trying gallery-dl as final fallback...")

            # Build command for gallery-dl
            cmd = ["gallery-dl"]

            # Output directory
            cmd.extend(["-d", destination])

            # Additional options
            cmd.extend(["--no-check-certificate"])

            cmd.append(url)

            # Run command
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=destination)

            if result.returncode == 0:
                return True
            else:
                self.status_var.set(f"gallery-dl failed: {result.stderr[:50]}...")
                return False

        except Exception as e:
            self.status_var.set(f"gallery-dl error: {str(e)[:50]}...")
            return False

    def try_youtube_dl_download(self, url, format_type, quality, destination):
        """Try downloading with youtube-dl using ClipGrab-like configuration"""
        try:
            self.status_var.set("Trying youtube-dl (ClipGrab method)...")

            # Simple configuration like ClipGrab
            ydl_opts = {
                'outtmpl': os.path.join(destination, '%(title)s.%(ext)s'),
                'ignoreerrors': True,
                'no_check_certificate': True,
                'no_warnings': False,
                'extractaudio': format_type == "mp3",
                'audioformat': 'mp3' if format_type == "mp3" else None,
                'audioquality': '192' if format_type == "mp3" else None,
            }

            if format_type == "mp4":
                if quality == "Highest":
                    ydl_opts['format'] = 'best[ext=mp4]/best'
                else:
                    height = quality.replace('p', '') if quality != "Highest" else "best"
                    ydl_opts['format'] = f'best[height<={height}][ext=mp4]/best[height<={height}]/best'

            with youtube_dl.YoutubeDL(ydl_opts) as ydl:
                # Extract info first
                self.status_var.set("Extracting video information...")
                info = ydl.extract_info(url, download=False)
                video_title = info.get('title', 'Unknown')

                self.status_var.set(f"Downloading with youtube-dl: {video_title}")
                ydl.download([url])

                return True

        except Exception as e:
            self.status_var.set(f"youtube-dl failed: {str(e)[:50]}...")
            return False

    def download_video(self):
        try:
            url = self.url_var.get().strip()
            format_type = self.format_var.get()
            quality = self.quality_var.get()
            destination = self.dest_var.get()

            # Try subprocess method first (direct command line like ClipGrab)
            success = self.try_subprocess_download(url, format_type, quality, destination)

            if success:
                self.status_var.set("✅ Download completed successfully!")
                return

            # Try youtube-dl API as second option
            success = self.try_youtube_dl_download(url, format_type, quality, destination)

            if success:
                self.status_var.set("✅ Download completed successfully!")
                return

            # Try gallery-dl as third option
            success = self.try_gallery_dl_download(url, format_type, quality, destination)

            if success:
                self.status_var.set("✅ Download completed successfully!")
                return

            # Configure yt-dlp options with multiple fallback strategies
            ydl_opts = {
                'outtmpl': os.path.join(destination, '%(title)s.%(ext)s'),
                'progress_hooks': [self.progress_hook],
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                },
                'extractor_args': {
                    'youtube': {
                        'player_client': ['android_music', 'android', 'web'],
                        'player_skip': ['webpage', 'configs'],
                        'skip': ['hls'],
                    }
                },
                'cookiefile': None,
                'no_warnings': True,
                'ignoreerrors': False,
                'extract_flat': False,
                'writethumbnail': False,
                'writeinfojson': False,
                'youtube_include_dash_manifest': False,
                'geo_bypass': True,
            }

            if format_type == "mp3":
                ydl_opts.update({
                    'format': 'bestaudio/best',
                    'postprocessors': [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': 'mp3',
                        'preferredquality': '192',
                    }],
                })
            else:  # mp4
                if quality == "Highest":
                    ydl_opts['format'] = 'best[ext=mp4]'
                else:
                    # Convert quality format (e.g., "720p" -> "720")
                    height = quality.replace('p', '') if quality != "Highest" else "best"
                    ydl_opts['format'] = f'best[height<={height}][ext=mp4]/best[ext=mp4]'

            # Try multiple extraction strategies with latest workarounds
            success = False
            strategies = [
                # Strategy 1: Android client with music workaround
                {
                    'extractor_args': {
                        'youtube': {
                            'player_client': ['android_music', 'android'],
                            'player_skip': ['webpage'],
                        }
                    }
                },
                # Strategy 2: iOS client
                {
                    'extractor_args': {
                        'youtube': {
                            'player_client': ['ios'],
                            'player_skip': ['webpage'],
                        }
                    }
                },
                # Strategy 3: Web client with bypass
                {
                    'extractor_args': {
                        'youtube': {
                            'player_client': ['web'],
                            'player_skip': ['webpage', 'configs'],
                        }
                    }
                },
                # Strategy 4: TV embedded client
                {
                    'extractor_args': {
                        'youtube': {
                            'player_client': ['tv_embedded'],
                        }
                    }
                },
                # Strategy 5: Basic with minimal config
                {
                    'extractor_args': {},
                    'youtube_include_dash_manifest': False,
                }
            ]

            for i, strategy in enumerate(strategies):
                try:
                    self.status_var.set(f"Trying extraction method {i+1}/{len(strategies)}...")

                    # Update options with current strategy
                    current_opts = ydl_opts.copy()
                    current_opts.update(strategy)

                    with yt_dlp.YoutubeDL(current_opts) as ydl:
                        # First try to extract info to validate the URL
                        self.status_var.set("Extracting video information...")
                        info = ydl.extract_info(url, download=False)
                        video_title = info.get('title', 'Unknown')

                        self.status_var.set(f"Starting download: {video_title}")
                        ydl.download([url])
                        success = True
                        break

                except yt_dlp.utils.ExtractorError as e:
                    if i < len(strategies) - 1:  # Not the last strategy
                        continue
                    else:
                        raise e  # Re-raise if all strategies failed

            # If yt-dlp failed, try youtube-dl as final fallback
            if not success:
                try:
                    self.status_var.set("Trying youtube-dl as fallback...")

                    # Configure youtube-dl options
                    ydl_opts_fallback = {
                        'outtmpl': os.path.join(destination, '%(title)s.%(ext)s'),
                        'quiet': False,
                        'no_warnings': False,
                    }

                    if format_type == "mp3":
                        ydl_opts_fallback.update({
                            'format': 'bestaudio/best',
                            'postprocessors': [{
                                'key': 'FFmpegExtractAudio',
                                'preferredcodec': 'mp3',
                                'preferredquality': '192',
                            }],
                        })
                    else:  # mp4
                        if quality == "Highest":
                            ydl_opts_fallback['format'] = 'best[ext=mp4]'
                        else:
                            height = quality.replace('p', '') if quality != "Highest" else "best"
                            ydl_opts_fallback['format'] = f'best[height<={height}][ext=mp4]/best[ext=mp4]'

                    with youtube_dl.YoutubeDL(ydl_opts_fallback) as ydl:
                        info = ydl.extract_info(url, download=False)
                        video_title = info.get('title', 'Unknown')

                        self.status_var.set(f"Downloading with youtube-dl: {video_title}")
                        ydl.download([url])
                        success = True

                except Exception as fallback_error:
                    raise Exception(f"All methods failed. Last error: {str(fallback_error)}")

            if not success:
                raise Exception("All extraction methods failed")

            self.status_var.set("✅ Download completed successfully!")

        except yt_dlp.utils.ExtractorError as e:
            error_msg = "Failed to extract video information. This could be due to:\n"
            error_msg += "• Video is private or unavailable\n"
            error_msg += "• Age-restricted content\n"
            error_msg += "• Geographic restrictions\n"
            error_msg += "• Invalid URL\n\n"
            error_msg += f"Technical details: {str(e)}"
            self.status_var.set("Error: Failed to extract video info")
            messagebox.showerror("Download Error", error_msg)

        except yt_dlp.utils.DownloadError as e:
            error_msg = f"Download failed: {str(e)}"
            self.status_var.set("Error: Download failed")
            messagebox.showerror("Download Error", error_msg)

        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            self.status_var.set(f"Error: {error_msg}")
            messagebox.showerror("Error", error_msg)

        finally:
            self.download_btn.configure(state="normal")
            self.progress_var.set(0)

    def progress_hook(self, d):
        if d['status'] == 'downloading':
            if 'total_bytes' in d and d['total_bytes']:
                percentage = d['downloaded_bytes'] / d['total_bytes']
                self.progress_var.set(percentage)
                self.status_var.set(f"Downloading... {percentage*100:.1f}%")
            elif '_percent_str' in d:
                # Fallback to percent string if total bytes not available
                percent_str = d['_percent_str'].strip()
                if percent_str and percent_str != 'N/A%':
                    try:
                        percentage = float(percent_str.replace('%', '')) / 100
                        self.progress_var.set(percentage)
                        self.status_var.set(f"Downloading... {percentage*100:.1f}%")
                    except ValueError:
                        pass
            self.update_idletasks()
        elif d['status'] == 'finished':
            self.progress_var.set(1.0)
            self.status_var.set("Processing...")
            self.update_idletasks()

if __name__ == "__main__":
    # Check for single instance
    instance_socket = check_single_instance()

    try:
        app = YouTubeDownloader()
        app.mainloop()
    finally:
        # Clean up socket
        if 'instance_socket' in locals():
            try:
                instance_socket.close()
            except:
                pass