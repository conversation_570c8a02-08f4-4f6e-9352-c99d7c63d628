#!/usr/bin/env python3

"""
INSTRUCTIONS FOR USER:
1. Open Terminal on your Mac
2. Navigate to this folder: cd /Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker
3. Run: source venv/bin/activate
4. Run: python RUN_THIS_ON_YOUR_MAC.py

This will test the exact issue and show you what's wrong.
"""

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

print("🔍 VS Mac Tools v2 - Direct Mac Test")
print("=" * 40)

# Test 1: Basic environment
print("1. Testing Python environment...")
import sys
print(f"   Python version: {sys.version}")
print(f"   Python path: {sys.executable}")

# Test 2: CustomTkinter
print("\n2. Testing CustomTkinter...")
try:
    import customtkinter as ctk
    print("   ✅ CustomTkinter imported successfully")
    
    # Create basic window
    print("   Creating basic CTk window...")
    app = ctk.CTk()
    app.title("Basic Test")
    app.geometry("300x200")
    
    label = ctk.CTkLabel(app, text="If you see this, CustomTkinter works!")
    label.pack(pady=20)
    
    button = ctk.CTkButton(app, text="Close", command=app.destroy)
    button.pack(pady=10)
    
    print("   ✅ Basic window created")
    print("   🎯 A window should appear now...")
    
    # Show window for 3 seconds
    app.after(3000, app.destroy)  # Auto-close after 3 seconds
    app.mainloop()
    
    print("   ✅ Basic CustomTkinter test PASSED")
    
except Exception as e:
    print(f"   ❌ CustomTkinter test FAILED: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: VS Mac Tools import
print("\n3. Testing VS Mac Tools import...")
try:
    from vs_mac_tools_v2 import InternetCheckerApp
    print("   ✅ VS Mac Tools imported successfully")
except Exception as e:
    print(f"   ❌ VS Mac Tools import FAILED: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 4: VS Mac Tools creation
print("\n4. Testing VS Mac Tools creation...")
try:
    print("   Creating InternetCheckerApp...")
    app = InternetCheckerApp()
    print("   ✅ InternetCheckerApp created")
    
    # Check if it has widgets
    children = app.winfo_children()
    print(f"   Widget count: {len(children)}")
    
    if len(children) == 0:
        print("   ❌ NO WIDGETS FOUND - THIS IS THE BLANK UI PROBLEM!")
        print("   The app creates but has no visible content.")
    else:
        print("   ✅ Widgets found:")
        for i, child in enumerate(children[:5]):
            print(f"      {i+1}. {type(child).__name__}")
    
    print("   🎯 VS Mac Tools window should appear now...")
    print("   If you see a blank window, that's the bug!")
    
    # Show for 5 seconds
    app.after(5000, app.destroy)
    app.mainloop()
    
    if len(children) > 0:
        print("   ✅ VS Mac Tools test PASSED")
    else:
        print("   ❌ VS Mac Tools has BLANK UI - widgets not created properly")
        
except Exception as e:
    print(f"   ❌ VS Mac Tools creation FAILED: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 40)
print("🎯 TEST COMPLETE")
print("\nIf you saw:")
print("  ✅ Basic CustomTkinter window with text - CustomTkinter works")
print("  ❌ Blank VS Mac Tools window - The bug is in the VS Mac Tools code")
print("\nPlease tell me what you saw!")
