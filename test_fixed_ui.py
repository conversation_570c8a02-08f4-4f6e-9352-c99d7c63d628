#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

print("🎯 Testing FIXED VS Mac Tools v2 UI")
print("=" * 40)

try:
    from vs_mac_tools_v2 import InternetCheckerApp
    
    print("Creating VS Mac Tools app...")
    app = InternetCheckerApp()
    
    print("✅ App created successfully!")
    print(f"📱 Title: {app.title()}")
    print(f"📐 Geometry: {app.geometry()}")
    
    # Check widgets
    children = app.winfo_children()
    print(f"👶 Widget count: {len(children)}")
    
    if len(children) > 0:
        print("✅ Widgets found - UI should NOT be blank!")
        for i, child in enumerate(children[:5]):
            print(f"   {i+1}. {type(child).__name__}")
    else:
        print("❌ NO widgets - UI will be blank")
    
    print("\n🎯 Starting app - UI should be visible now!")
    print("   Close the window when you're done testing...")
    
    # Run the app
    app.mainloop()
    
    print("✅ App closed successfully")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 Test complete!")
print("Did you see the full VS Mac Tools UI with all frames and content?")
