#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

import customtkinter as ctk
import subprocess
import threading
import time
from tkinter import messagebox
from datetime import datetime
import queue

# Set CustomTkinter appearance - EXACTLY like youtube_downloader.py
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class VSMacToolsApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("VS Mac Tools v2 - Simple")
        self.geometry("800x600")
        
        # Initialize variables
        self.result_queue = queue.Queue()
        
        # Setup UI using EXACT same pattern as youtube_downloader.py
        self.setup_ui()
        
        # Start background tasks
        self.start_background_tasks()
    
    def setup_ui(self):
        """Setup UI using EXACT same pattern as youtube_downloader.py"""
        
        # Process Steps Frame - EXACT same pattern as youtube_downloader.py
        process_frame = ctk.CTkFrame(self)
        process_frame.pack(fill="x", padx=20, pady=20)
        
        ctk.CTkLabel(process_frame, text="Process Steps").pack(side="left", padx=(10, 5), pady=10)
        
        # Execute button - EXACT same pattern as youtube_downloader.py
        self.execute_btn = ctk.CTkButton(process_frame, text="Execute All", 
                                        command=self.execute_all, width=120)
        self.execute_btn.pack(side="right", padx=(5, 10), pady=10)
        
        # Process steps checkboxes - EXACT same pattern as youtube_downloader.py
        steps_frame = ctk.CTkFrame(self)
        steps_frame.pack(fill="x", padx=20, pady=10)
        
        self.steps = [
            "Check Time on time.apple.com",
            "Check Apple Server Connection", 
            "Check ADE/DEP Enrollment",
            "Check MDM Enrollment Status",
            "Check Installed Profiles",
            "Show Device Enrollment Log",
            "Remove Paired Bluetooth Devices"
        ]
        
        self.checkboxes = []
        for step in self.steps:
            checkbox = ctk.CTkCheckBox(steps_frame, text=step, state="disabled")
            checkbox.pack(anchor="w", padx=10, pady=2)
            self.checkboxes.append(checkbox)
        
        # System Information Frame - EXACT same pattern as youtube_downloader.py
        info_frame = ctk.CTkFrame(self)
        info_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(info_frame, text="System Information").pack(side="left", padx=(10, 5), pady=10)
        
        # Save buttons - EXACT same pattern as youtube_downloader.py
        self.save_csv_btn = ctk.CTkButton(info_frame, text="Save CSV", 
                                         command=self.save_csv, width=80)
        self.save_csv_btn.pack(side="right", padx=(5, 5), pady=10)
        
        self.save_xml_btn = ctk.CTkButton(info_frame, text="Save XML", 
                                         command=self.save_xml, width=80)
        self.save_xml_btn.pack(side="right", padx=(0, 5), pady=10)
        
        # Hardware info display - EXACT same pattern as youtube_downloader.py
        hardware_frame = ctk.CTkFrame(self)
        hardware_frame.pack(fill="x", padx=20, pady=10)
        
        self.hardware_text = ctk.CTkTextbox(hardware_frame, height=100)
        self.hardware_text.pack(fill="x", padx=10, pady=10)
        
        # Shortcuts Frame - EXACT same pattern as youtube_downloader.py
        shortcuts_frame = ctk.CTkFrame(self)
        shortcuts_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(shortcuts_frame, text="Shortcuts").pack(side="left", padx=(10, 5), pady=10)
        
        # Shortcut buttons - EXACT same pattern as youtube_downloader.py
        btn_container = ctk.CTkFrame(shortcuts_frame, fg_color="transparent")
        btn_container.pack(side="right", padx=10, pady=10)
        
        self.bluetooth_btn = ctk.CTkButton(btn_container, text="Bluetooth", 
                                          command=self.open_bluetooth, width=80)
        self.bluetooth_btn.pack(side="left", padx=(0, 5))
        
        self.findmy_btn = ctk.CTkButton(btn_container, text="FindMy", 
                                       command=self.open_findmy, width=80)
        self.findmy_btn.pack(side="left", padx=(0, 5))
        
        self.exit_btn = ctk.CTkButton(btn_container, text="Exit", 
                                     command=self.quit_app, width=80,
                                     fg_color="Indian red", hover_color="Red")
        self.exit_btn.pack(side="left", padx=(5, 0))
        
        # Process Logs Frame - EXACT same pattern as youtube_downloader.py
        logs_frame = ctk.CTkFrame(self)
        logs_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(logs_frame, text="Process Logs").pack(pady=(10, 5))
        
        self.result_text = ctk.CTkTextbox(logs_frame, wrap="word")
        self.result_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Status bar - EXACT same pattern as youtube_downloader.py
        status_frame = ctk.CTkFrame(self)
        status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.status_var = ctk.StringVar(value="Ready")
        self.status_label = ctk.CTkLabel(status_frame, textvariable=self.status_var)
        self.status_label.pack(side="left", padx=10, pady=5)
        
        self.datetime_label = ctk.CTkLabel(status_frame, text="")
        self.datetime_label.pack(side="right", padx=10, pady=5)
        
        # Initialize data
        self.update_hardware_info()
        self.update_datetime()
    
    def start_background_tasks(self):
        """Start background tasks - EXACT same pattern as youtube_downloader.py"""
        # Start queue checker
        self.after(100, self.check_result_queue)
        
        # Start datetime updater
        self.after(1000, self.update_datetime_recurring)
    
    def update_datetime(self):
        """Update datetime once"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=current_time)
    
    def update_datetime_recurring(self):
        """Update datetime and schedule next update"""
        self.update_datetime()
        self.after(1000, self.update_datetime_recurring)
    
    def check_result_queue(self):
        """Check for results from background threads"""
        try:
            while True:
                result = self.result_queue.get_nowait()
                self.display_result(result)
        except queue.Empty:
            pass
        finally:
            self.after(100, self.check_result_queue)
    
    def display_result(self, result):
        """Display result in the text area"""
        self.result_text.insert("end", f"{result}\n\n")
        self.result_text.see("end")
        self.status_var.set("Process completed")
    
    def execute_all(self):
        """Execute all process steps"""
        self.execute_btn.configure(state="disabled")
        self.status_var.set("Executing all steps...")
        
        # Start execution in a separate thread
        threading.Thread(target=self.execute_all_thread, daemon=True).start()
    
    def execute_all_thread(self):
        """Execute all steps in background thread"""
        for i, step in enumerate(self.steps):
            try:
                self.result_queue.put(f"Executing: {step}")
                
                # Simulate work
                time.sleep(1)
                
                # Mark checkbox as completed
                self.after(0, lambda idx=i: self.checkboxes[idx].select())
                
                self.result_queue.put(f"✅ {step} - Completed")
                
            except Exception as e:
                self.result_queue.put(f"❌ Error in {step}: {str(e)}")
        
        # Re-enable button
        self.after(0, lambda: self.execute_btn.configure(state="normal"))
        self.after(0, lambda: self.status_var.set("All steps completed"))
    
    def update_hardware_info(self):
        """Update hardware information"""
        try:
            # Get basic system info
            model = subprocess.check_output("sysctl -n hw.model", shell=True, text=True).strip()
            memory = subprocess.check_output("sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'", shell=True, text=True).strip()
            version = subprocess.check_output("sw_vers -productVersion", shell=True, text=True).strip()
            
            info_text = f"Model: {model}\nMemory: {memory}\nmacOS: {version}\n"
            self.hardware_text.delete("1.0", "end")
            self.hardware_text.insert("1.0", info_text)
            
        except Exception as e:
            self.hardware_text.delete("1.0", "end")
            self.hardware_text.insert("1.0", f"Error getting system info: {e}")
    
    def save_csv(self):
        """Save to CSV"""
        self.status_var.set("CSV save functionality would go here")
        messagebox.showinfo("Save CSV", "CSV export functionality")
    
    def save_xml(self):
        """Save to XML"""
        self.status_var.set("XML save functionality would go here")
        messagebox.showinfo("Save XML", "XML export functionality")
    
    def open_bluetooth(self):
        """Open Bluetooth settings"""
        os.system("open /System/Library/PreferencePanes/Bluetooth.prefPane")
        self.status_var.set("Bluetooth settings opened")
    
    def open_findmy(self):
        """Open FindMy"""
        os.system("open -a FindMy")
        self.status_var.set("FindMy opened")
    
    def quit_app(self):
        """Exit the application"""
        self.quit()
        self.destroy()

def main():
    app = VSMacToolsApp()
    app.mainloop()

if __name__ == "__main__":
    main()
