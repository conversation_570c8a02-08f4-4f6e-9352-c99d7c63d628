#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

import customtkinter as ctk
import subprocess
import threading
import time
import csv
import xml.dom.minidom
import xml.etree.ElementTree as ET
from tkinter import filedialog, messagebox
from datetime import datetime
import queue
from PIL import Image
import sys
import pathlib
import platform
import keyring
import getpass
from password_dialog import get_sudo_password

# Set CustomTkinter appearance - EXACTLY like youtube_downloader.py
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# Default passwords
SUDO_PASSWORD = "1111"
WIFI_PASSWORD = None

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def get_macos_version():
    """Get the macOS version as a tuple of integers (major, minor, patch)"""
    version_str = platform.mac_ver()[0]
    version_parts = version_str.split('.')
    while len(version_parts) < 3:
        version_parts.append('0')
    return tuple(int(part) for part in version_parts[:3])

MACOS_VERSION = get_macos_version()

class VSMacToolsApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Apple ADE Checker v2.0.1")
        self.geometry("1120x890")
        
        # Network configuration
        self.network_path = "/Volumes/share"
        self.server_address = "**************"
        self.share_name = "share"
        self.network_config = {
            "mount_point": self.network_path,
            "server_address": self.server_address,
            "share_name": self.share_name,
            "smb_url": f"smb://{self.server_address}/{self.share_name}"
        }
        
        # Initialize variables
        self.result_queue = queue.Queue()
        self.internet_connected = True
        
        # Load icons
        self.load_icons()
        
        # Setup UI
        self.setup_ui()
        
        # Initialize data
        self.update_datetime()
        self.update_hardware_info()
        
        # Start background tasks
        self.start_background_tasks()
    
    def load_icons(self):
        """Load all icons"""
        self.icons = {}
        icon_files = {
            "bluetooth": "img/bluetooth.icns",
            "findmy": "img/findmy.icns", 
            "profile": "img/device.icns",
            "system": "img/sysinfo.icns",
            "shutdown": "img/shutdown.png",
            "erase": "img/erase.png",
            "exit": "img/exit.png"
        }
        
        for name, path in icon_files.items():
            try:
                full_path = get_resource_path(path)
                if os.path.exists(full_path):
                    image = Image.open(full_path)
                    self.icons[name] = ctk.CTkImage(image, size=(20, 20))
                else:
                    self.icons[name] = None
            except Exception as e:
                print(f"Error loading icon {path}: {e}")
                self.icons[name] = None
    
    def setup_ui(self):
        """Setup the user interface - using the working pattern from youtube_downloader.py"""
        
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        
        # Process Steps Frame
        process_frame = ctk.CTkFrame(self, width=260, height=255)
        process_frame.grid(row=0, column=0, padx=(10,3), pady=(3, 3), sticky="new")
        process_frame.grid_propagate(False)
        
        ctk.CTkLabel(process_frame, text="Process Steps", font=("Arial", 16, "bold")).pack(pady=10)
        
        # Process steps
        self.buttons = [
            ("Check Time on time.apple.com", self.check_apple_time),
            ("Check Apple Server Connection", self.check_apple_server_connection),
            ("Check ADE/DEP Enrollment", self.check_dep_enrollment),
            ("Check MDM Enrollment Status", lambda: self.run_sudo_command("sudo profiles status -type enrollment")),
            ("Check Installed Profiles", self.check_profiles_installed),
            ("Show Device Enrollment Log", self.show_device_enrollment_log),
            ("Remove Paired Bluetooth Devices", self.delete_bluetooth_devices)
        ]
        
        self.checkboxes = []
        for i, (text, _) in enumerate(self.buttons):
            checkbox = ctk.CTkCheckBox(process_frame, text=text, state="disabled", font=("Arial", 10))
            checkbox.pack(anchor="w", padx=10, pady=2)
            self.checkboxes.append(checkbox)
        
        # Execute button
        execute_btn = ctk.CTkButton(process_frame, text="Execute All", command=self.auto_execute)
        execute_btn.pack(pady=10)
        
        # Process Logs Frame
        logs_frame = ctk.CTkFrame(self)
        logs_frame.grid(row=0, column=1, rowspan=3, padx=(5, 5), pady=3, sticky="nsew")
        logs_frame.grid_columnconfigure(0, weight=1)
        logs_frame.grid_rowconfigure(1, weight=1)
        
        ctk.CTkLabel(logs_frame, text="Process Logs", font=("Arial", 16, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        
        self.result_text = ctk.CTkTextbox(logs_frame, wrap="word", font=("Monaco", 12))
        self.result_text.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        
        # System Information Frame
        hardware_frame = ctk.CTkFrame(self, width=270, height=300)
        hardware_frame.grid(row=1, column=0, padx=(10,3), pady=3, sticky="new")
        hardware_frame.grid_propagate(False)
        
        ctk.CTkLabel(hardware_frame, text="System Information", font=("Arial", 16, "bold")).pack(pady=10)
        
        # Hardware info
        self.hardware_info = {}
        info_keys = ["Model Identifier", "Chip", "Memory", "SSD Storage", "Serial number", "macOS Version", "Activation Lock Status"]
        
        for key in info_keys:
            frame = ctk.CTkFrame(hardware_frame, fg_color="transparent")
            frame.pack(fill="x", padx=10, pady=2)
            
            ctk.CTkLabel(frame, text=f"{key}:", font=("Arial", 10)).pack(side="left")
            label = ctk.CTkLabel(frame, text="", font=("Arial", 10))
            label.pack(side="right")
            self.hardware_info[key] = label
        
        # Save buttons
        save_frame = ctk.CTkFrame(hardware_frame, fg_color="transparent")
        save_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkButton(save_frame, text="Save to CSV", command=self.save_hardware_overview, width=100).pack(side="left", padx=5)
        ctk.CTkButton(save_frame, text="Save to XML", command=self.save_hardware_to_xml, width=100).pack(side="right", padx=5)
        
        # Shortcuts Frame
        shortcuts_frame = ctk.CTkFrame(self, width=260, height=270)
        shortcuts_frame.grid(row=2, column=0, padx=(10,3), pady=(3, 3), sticky="new")
        shortcuts_frame.grid_propagate(False)
        
        ctk.CTkLabel(shortcuts_frame, text="Shortcuts", font=("Arial", 16, "bold")).pack(pady=10)
        
        # Shortcut buttons
        shortcuts = [
            ("Bluetooth", self.open_bluetooth_settings),
            ("FindMy", self.open_findmy),
            ("Device Management Profile", self.open_system_preferences),
            ("System Information", self.show_system_info),
            ("Shutdown System", self.shutdown_system),
            ("Erase all content and settings..", self.erase_app),
            ("Exit", self.exit_app)
        ]
        
        for name, command in shortcuts:
            icon = self.icons.get(name.lower().split()[0], None)
            btn = ctk.CTkButton(shortcuts_frame, text=name, image=icon, command=command, anchor="w")
            btn.pack(fill="x", padx=10, pady=2)
        
        # Status bar
        status_frame = ctk.CTkFrame(self, height=30)
        status_frame.grid(row=3, column=0, columnspan=2, padx=10, pady=(0, 5), sticky="ew")
        
        self.status_label = ctk.CTkLabel(status_frame, text="Internet connection")
        self.status_label.pack(side="left", padx=10, pady=5)
        
        self.datetime_label = ctk.CTkLabel(status_frame, text="", font=("Monaco", 12))
        self.datetime_label.pack(side="right", padx=10, pady=5)
    
    def start_background_tasks(self):
        """Start background tasks"""
        # Start queue checker
        self.after(100, self.check_result_queue)
        
        # Start datetime updater
        self.after(1000, self.update_datetime_recurring)
    
    def update_datetime(self):
        """Update datetime once"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=current_time)
    
    def update_datetime_recurring(self):
        """Update datetime and schedule next update"""
        self.update_datetime()
        self.after(1000, self.update_datetime_recurring)
    
    def check_result_queue(self):
        """Check for results from background threads"""
        try:
            while True:
                result = self.result_queue.get_nowait()
                self.display_result(result)
        except queue.Empty:
            pass
        finally:
            self.after(100, self.check_result_queue)
    
    def display_result(self, result):
        """Display result in the text area"""
        self.result_text.insert("end", f"{result}\n\n")
        self.result_text.see("end")
    
    def auto_execute(self):
        """Execute all process steps"""
        threading.Thread(target=self._auto_execute_thread, daemon=True).start()
    
    def _auto_execute_thread(self):
        """Execute all steps in background thread"""
        for i, (text, command) in enumerate(self.buttons):
            try:
                if callable(command):
                    result = command()
                else:
                    result = self.run_command(command)
                
                self.result_queue.put(f"{text}:\n{result}")
                self.after(0, lambda idx=i: self.checkboxes[idx].select())
            except Exception as e:
                self.result_queue.put(f"Error executing {text}: {str(e)}")
            time.sleep(1)
    
    def run_command(self, command):
        """Run a shell command"""
        try:
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            return output.strip()
        except subprocess.CalledProcessError as e:
            return f"Error: {e.output.strip()}"
        except subprocess.TimeoutExpired:
            return "Error: Command timed out"
    
    def run_sudo_command(self, command):
        """Run a command with sudo"""
        global SUDO_PASSWORD
        full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
        return self.run_command(full_command)
    
    # Placeholder methods for all the functionality
    def check_apple_time(self):
        return "Time check completed"
    
    def check_apple_server_connection(self):
        return "Apple server connection checked"
    
    def check_dep_enrollment(self):
        return "DEP enrollment checked"
    
    def check_profiles_installed(self):
        return "Profiles checked"
    
    def show_device_enrollment_log(self):
        return "Device enrollment log displayed"
    
    def delete_bluetooth_devices(self):
        return "Bluetooth devices removed"
    
    def open_bluetooth_settings(self):
        os.system("open /System/Library/PreferencePanes/Bluetooth.prefPane")
        return "Bluetooth settings opened"
    
    def open_findmy(self):
        os.system("open -a FindMy")
        return "FindMy opened"
    
    def open_system_preferences(self):
        os.system("open /System/Library/PreferencePanes/Profiles.prefPane")
        return "System preferences opened"
    
    def show_system_info(self):
        os.system("open /Applications/Utilities/System\\ Information.app")
        return "System Information opened"
    
    def shutdown_system(self):
        return "Shutdown dialog displayed"
    
    def erase_app(self):
        return "Erase dialog displayed"
    
    def exit_app(self):
        self.quit()
    
    def update_hardware_info(self):
        """Update hardware information"""
        info = self.get_hardware_info()
        for key, label in self.hardware_info.items():
            if key in info:
                label.configure(text=info[key])
    
    def get_hardware_info(self):
        """Get hardware information"""
        commands = {
            "Model Identifier": "sysctl -n hw.model",
            "Chip": "sysctl -n machdep.cpu.brand_string",
            "Memory": "sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'",
            "SSD Storage": "diskutil info / | grep 'Disk Size' | awk '{print $3 \" \" $4}'",
            "Serial number": "ioreg -c IOPlatformExpertDevice -d 2 | grep -i 'IOPlatformSerialNumber' | awk -F '\"' '{print $4}'",
            "macOS Version": "sw_vers -productVersion",
            "Activation Lock Status": "system_profiler SPHardwareDataType | grep -i 'Activation Lock' | awk -F ': ' '{print $2}'"
        }
        
        info = {}
        for key, command in commands.items():
            try:
                output = subprocess.check_output(command, shell=True, text=True).strip()
                info[key] = output.strip('"')
            except subprocess.CalledProcessError:
                info[key] = "Unable to retrieve"
        return info
    
    def save_hardware_overview(self):
        """Save hardware information to CSV"""
        info = self.get_hardware_info()
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")],
            initialdir=os.path.expanduser("~/Desktop")
        )
        if file_path:
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(["Property", "Value"])
                for key, value in info.items():
                    writer.writerow([key, value])
            self.display_result(f"System Information saved to {file_path}")
    
    def save_hardware_to_xml(self):
        """Save hardware information to XML"""
        self.display_result("XML export functionality would go here")

def main():
    app = VSMacToolsApp()
    app.mainloop()

if __name__ == "__main__":
    main()
