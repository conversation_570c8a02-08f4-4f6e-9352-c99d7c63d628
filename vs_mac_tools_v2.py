
import socket
import subprocess
import customtkinter as ctk
import threading
import os
import time
import csv
import xml.dom.minidom
import xml.etree.ElementTree as ET
from tkinter import filedialog
from datetime import datetime
import queue
from PIL import Image
import sys
import pathlib
import platform
import keyring
import getpass
from password_dialog import get_sudo_password

# Disable Tkinter menu bar creation which causes crashes on macOS vs
os.environ['TKINTER_NO_MACOS_MENUBAR'] = '1'

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

# Default passwords
SUDO_PASSWORD = "1111"  # Default sudo password, will try this first
WIFI_PASSWORD = None  # WiFi password for automatic connection

# Check macOS version
def get_macos_version():
    """Get the macOS version as a tuple of integers (major, minor, patch)"""
    version_str = platform.mac_ver()[0]
    version_parts = version_str.split('.')
    # Ensure we have at least 3 parts, padding with zeros if needed
    while len(version_parts) < 3:
        version_parts.append('0')
    return tuple(int(part) for part in version_parts[:3])

# Get macOS version
MACOS_VERSION = get_macos_version()

class InternetCheckerApp(ctk.CTk):
    def __init__(self):
        try:
            super().__init__()
        except Exception as e:
            print(f"Error initializing CTk: {str(e)}")
            # Try to continue with basic initialization
            pass

        # Network share configuration
        self.network_path = "/Volumes/share"  # Lowercase 's' to match SMB URL
        self.server_address = "**************"
        self.share_name = "share"  # Lowercase 's' to match SMB URL

        # Add network_config dictionary
        self.network_config = {
            "mount_point": self.network_path,
            "server_address": self.server_address,
            "share_name": self.share_name,
            "smb_url": f"smb://{self.server_address}/{self.share_name}"
        }

        # Try to mount the network share at startup
        self.mount_network_share_at_startup()

        # Load icons from the img folder using resource path
        self.icons = {
            "bluetooth": self.load_icon(get_resource_path("img/bluetooth.icns"), (20, 20)),
            "findmy": self.load_icon(get_resource_path("img/findmy.icns"), (20, 20)),
            "profile": self.load_icon(get_resource_path("img/device.icns"), (20, 20)),
            "system": self.load_icon(get_resource_path("img/sysinfo.icns"), (20, 20)),
            "shutdown": self.load_icon(get_resource_path("img/shutdown.png"), (20, 20)),
            "erase": self.load_icon(get_resource_path("img/erase.png"), (20, 20)),
            "exit": self.load_icon(get_resource_path("img/exit.png"), (20, 20))
        }
        # Configure window
        self.title("Apple ADE Checker v2.0.1")
        self.geometry("1120x890")

        # Set minimum window size to prevent resizing below Process Steps frame height
        # Minimum height = Process Steps frame (280px) + padding + status bar ≈ 350px
        self.minsize(800, 300)

        # Set macOS-like appearance
        ctk.set_appearance_mode("dark")

        # macOS-style colors
        self.macos_colors = {
            "window_bg": "#1E1E1E",
            "frame_bg": "#2A2A2A",
            "frame_border": "#404040",
            "text_primary": "#FFFFFF",
            "text_secondary": "#B3B3B3",
            "accent_blue": "#007AFF",
            "accent_blue_hover": "#0056CC",
            "success_green": "#34C759",
            "warning_orange": "#FF9500",
            "Indian_red": "#D7554E",
            "error_red": "#FF3B30"

        }

        # Configure main window background
        self.configure(fg_color=self.macos_colors["window_bg"])

        # Configure grid weights - ONLY right column expands, rows have NO weight (fixed heights)
        self.grid_columnconfigure(1, weight=1)  # Right column expands
        # Remove row weights to prevent frame height changes
        # self.grid_rowconfigure(0, weight=1)     # No weight = fixed height
        # self.grid_rowconfigure(1, weight=1)     # No weight = fixed height
        # self.grid_rowconfigure(2, weight=1)     # No weight = fixed height

        # Process Steps Frame with macOS-style border - FIXED HEIGHT
        self.process_frame = ctk.CTkFrame(
            self,
            width=260,
            height=255,
            fg_color=self.macos_colors["frame_bg"],
            border_width=1,
            border_color=self.macos_colors["frame_border"],
            corner_radius=8
        )
        self.process_frame.grid(row=0, column=0, padx=5, pady=(3, 3), sticky="new")
        self.process_frame.grid_propagate(False)
        self.process_frame.grid_columnconfigure(0, weight=1)
        # Process steps header
        self.process_steps_label = ctk.CTkLabel(
            self.process_frame,
            text="Process Steps",
            font=("SF Pro Display", 16, "bold"),
            text_color=self.macos_colors["text_primary"]
        )
        self.process_steps_label.grid(row=0, column=0, padx=5, pady=(3, 3), sticky="w")

        # Define buttons and their corresponding functions
        self.buttons = [
            ("Check Time on time.apple.com", self.check_apple_time),
            ("Check Apple Server Connection", self.check_apple_server_connection),
            ("Check ADE/DEP Enrollment", self.check_dep_enrollment),
            ("Check MDM Enrollment Status", lambda: self.run_sudo_command("sudo profiles status -type enrollment")),
            ("Check Installed Profiles", self.check_profiles_installed),
            ("Show Device Enrollment Log", self.show_device_enrollment_log),
            ("Remove Paired Bluetooth Devices", self.delete_bluetooth_devices)
        ]

        # Checkboxes for process steps with improved styling
        self.checkboxes = []
        for i, (text, _) in enumerate(self.buttons):
            checkbox = ctk.CTkCheckBox(
                self.process_frame,
                text=text,
                state="disabled",
                width=20,
                height=20,
                checkbox_width=16,
                checkbox_height=16,
                font=("SF Mono", 12),
                border_width=1,
                text_color="#FFFFFF",
                text_color_disabled="#FFFFFF",
                fg_color=self.macos_colors["accent_blue"],
                hover_color=self.macos_colors["accent_blue_hover"]
            )
            checkbox.grid(row=i+1, column=0, padx=8, pady=3, sticky="w")
            # Force white text color after creation
            checkbox.configure(text_color="#FFFFFF")
            self.checkboxes.append(checkbox)

        # Execute All button with improved styling
        self.execute_all_button = ctk.CTkButton(
            self.process_frame,
            text="Execute All",
            command=self.auto_execute,
            width=120,
            height=28,
            anchor="center",
            font=("SF Pro", 12),
            fg_color=self.macos_colors["accent_blue"],
            hover_color=self.macos_colors["accent_blue_hover"],
            corner_radius=6
        )
        self.execute_all_button.grid(row=len(self.buttons) + 1, column=0, padx=55, pady=(3, 3), sticky="w")
        # Right Frame (Process Logs) with macOS-style border
        self.right_frame = ctk.CTkFrame(
            self,
            fg_color=self.macos_colors["frame_bg"],
            border_width=1,
            border_color=self.macos_colors["frame_border"],
            corner_radius=6
        )
        self.right_frame.grid(row=0, column=1, rowspan=3, padx=(5, 5), pady=3, sticky="nsew")
        self.right_frame.grid_columnconfigure(0, weight=1)
        self.right_frame.grid_rowconfigure(1, weight=1)

        # Process logs header
        self.process_logs_label = ctk.CTkLabel(
            self.right_frame,
            text="Process Logs",
            font=("SF Pro Display", 16, "bold"),
            text_color=self.macos_colors["text_primary"]
        )
        self.process_logs_label.grid(row=0, column=0, padx=5, pady=(3, 3), sticky="w")

        # Process logs textbox with improved styling
        self.result_text = ctk.CTkTextbox(
            self.right_frame,
            wrap="word",
            fg_color="#1A1A1A",
            border_width=1,
            border_color="#333333",
            corner_radius=4,
            font=("SF Mono", 12),
            text_color=self.macos_colors["text_primary"]
        )
        self.result_text.grid(row=1, column=0, padx=5, pady=(0, 5), sticky="nsew")

        # System Information Frame with macOS-style border - FIXED HEIGHT
        self.hardware_frame = ctk.CTkFrame(
            self,
            width=270,
            height=300,
            fg_color=self.macos_colors["frame_bg"],
            border_width=1,
            border_color=self.macos_colors["frame_border"],
            corner_radius=6
        )
        self.hardware_frame.grid(row=1, column=0, padx=5, pady=3, sticky="new")
        self.hardware_frame.grid_propagate(False)
        self.hardware_frame.grid_columnconfigure(0, weight=1)
        self.hardware_frame.grid_columnconfigure(1, weight=2)

        # System information header
        self.hardware_label = ctk.CTkLabel(
            self.hardware_frame,
            text="System Information",
            font=("SF Pro Display", 16, "bold"),
            text_color=self.macos_colors["text_primary"]
        )
        self.hardware_label.grid(row=0, column=0, columnspan=2, padx=10, pady=(3, 3), sticky="w")
        self.hardware_info = {
            "Model Identifier": None,
            "Chip": None,
            "Memory": None,
            "SSD Storage": None,
            "Serial number": None,
            "macOS Version": None,
            "Activation Lock Status": None
        }
        # System information labels with improved styling
        for i, (key, _) in enumerate(self.hardware_info.items()):
            # Label for the key
            label = ctk.CTkLabel(
                self.hardware_frame,
                text=f"{key}:",
                font=("SF Pro", 12),
                text_color=self.macos_colors["text_secondary"]
            )
            label.grid(row=i+1, column=0, padx=(5, 5), pady=1, sticky="e")

            # Value label with special styling for Activation Lock Status
            if key == "Activation Lock Status":
                self.hardware_info[key] = ctk.CTkLabel(
                    self.hardware_frame,
                    text="",
                    font=("SF Pro", 12, "bold"),
                    anchor="w",
                    text_color=self.macos_colors["text_secondary"]
                )
            else:
                self.hardware_info[key] = ctk.CTkLabel(
                    self.hardware_frame,
                    text="",
                    font=("SF Pro", 12),
                    anchor="w",
                    text_color=self.macos_colors["text_primary"]
                )

            self.hardware_info[key].grid(row=i+1, column=1, padx=(5, 5), pady=2, sticky="w")

        # Create a frame for the save buttons with transparent background
        self.save_buttons_frame = ctk.CTkFrame(
            self.hardware_frame,
            fg_color="transparent"
        )
        self.save_buttons_frame.grid(row=len(self.hardware_info)+1, column=0, columnspan=2, pady=(3, 3), padx=10, sticky="ew")
        self.save_buttons_frame.grid_columnconfigure(0, weight=1)
        self.save_buttons_frame.grid_columnconfigure(1, weight=1)

        # Save to CSV button with improved styling
        self.save_csv_button = ctk.CTkButton(
            self.save_buttons_frame,
            text="Save to CSV",
            command=self.save_hardware_overview,
            font=("SF Pro", 12),
            fg_color=self.macos_colors["accent_blue"],
            hover_color=self.macos_colors["accent_blue_hover"],
            text_color="white",
            corner_radius=4,
            height=28
        )
        self.save_csv_button.grid(row=0, column=0, pady=0, padx=(0, 3), sticky="ew")

        # Save to XML button with improved styling
        self.save_xml_button = ctk.CTkButton(
            self.save_buttons_frame,
            text="Save to XML",
            command=self.save_hardware_to_xml,
            font=("SF Pro", 12),
            fg_color=self.macos_colors["accent_blue"],
            hover_color=self.macos_colors["accent_blue_hover"],
            text_color="white",
            corner_radius=4,
            height=28
        )
        self.save_xml_button.grid(row=0, column=1, pady=0, padx=(4, 0), sticky="ew")

        # Shortcuts Frame with macOS-style border - FIXED HEIGHT
        self.shortcuts_frame = ctk.CTkFrame(
            self,
            width=260,
            height=270,
            fg_color=self.macos_colors["frame_bg"],
            border_width=1,
            border_color=self.macos_colors["frame_border"],
            corner_radius=8
        )
        self.shortcuts_frame.grid(row=2, column=0, padx=5, pady=(3, 3), sticky="new")
        self.shortcuts_frame.grid_propagate(False)
        self.shortcuts_frame.grid_columnconfigure(0, weight=1)

        # Shortcuts header
        self.shortcuts_label = ctk.CTkLabel(
            self.shortcuts_frame,
            text="Shortcuts",
            font=("SF Pro Display", 16, "bold"),
            text_color=self.macos_colors["text_primary"]
        )
        self.shortcuts_label.grid(row=0, column=0, padx=5, pady=(3, 3), sticky="w")

        # Define macOS style button colors for shortcuts
        macos_button_color = "#333333"  # Slightly lighter than frame background
        macos_hover_color = "#404040"   # Subtle hover effect
        macos_text_color = self.macos_colors["text_primary"]

        # Bluetooth button with improved styling
        self.open_bluetooth_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="Bluetooth",
            image=self.icons["bluetooth"],
            command=self.open_bluetooth_settings,
            anchor="w",
            font=("SF Pro", 12),
            fg_color=macos_button_color,
            hover_color=macos_hover_color,
            text_color=macos_text_color,
            corner_radius=4,
            height=28
        )
        self.open_bluetooth_button.grid(row=1, column=0, pady=2, padx=10, sticky="ew")

        # FindMy button with improved styling
        self.open_findmy_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="FindMy",
            image=self.icons["findmy"],
            command=self.open_findmy,
            anchor="w",
            font=("SF Pro", 12),
            fg_color=macos_button_color,
            hover_color=macos_hover_color,
            text_color=macos_text_color,
            corner_radius=4,
            height=28
        )
        self.open_findmy_button.grid(row=2, column=0, pady=2, padx=10, sticky="ew")

        # Profile button with improved styling
        self.open_profile_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="Device Management Profile",
            image=self.icons["profile"],
            command=self.open_system_preferences,
            anchor="w",
            font=("SF Pro", 12),
            fg_color=macos_button_color,
            hover_color=macos_hover_color,
            text_color=macos_text_color,
            corner_radius=4,
            height=28
        )
        self.open_profile_button.grid(row=3, column=0, pady=2, padx=10, sticky="ew")

        # System Information button with improved styling
        self.sys_info_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="System Information",
            image=self.icons["system"],
            command=self.show_system_info,
            anchor="w",
            font=("SF Pro", 12),
            fg_color=macos_button_color,
            hover_color=macos_hover_color,
            text_color=macos_text_color,
            corner_radius=4,
            height=28
        )
        self.sys_info_button.grid(row=4, column=0, pady=2, padx=10, sticky="ew")

        # Shutdown button with improved styling
        self.shutdown_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="Shutdown System",
            image=self.icons["shutdown"],
            command=self.shutdown_system,
            anchor="w",
            font=("SF Pro", 12),
            fg_color=macos_button_color,
            hover_color=macos_hover_color,
            text_color=macos_text_color,
            corner_radius=4,
            height=28
        )
        self.shutdown_button.grid(row=5, column=0, pady=2, padx=10, sticky="ew")

        # Erase all content button with improved styling
        self.erase_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="Erase all content and settings..",
            image=self.icons["erase"],
            command=self.erase_app,
            anchor="w",
            font=("SF Pro", 12),
            fg_color=macos_button_color,
            hover_color=macos_hover_color,
            text_color=macos_text_color,
            corner_radius=4,
            height=28
        )
        self.erase_button.grid(row=6, column=0, pady=2, padx=10, sticky="ew")

        # Exit button with accent color
        self.exit_button = ctk.CTkButton(
            self.shortcuts_frame,
            text="Exit",
            image=self.icons["exit"],
            command=self.exit_app,
            anchor="center",
            font=("SF Pro", 12),
            fg_color=self.macos_colors["Indian_red"],
            hover_color=self.macos_colors["error_red"],
            text_color="white",
            corner_radius=4,
            height=28
        )
        self.exit_button.grid(row=7, column=0, pady=(5, 5), padx=10, sticky="ew")
        # Status bar frame with subtle border
        self.status_frame = ctk.CTkFrame(
            self,
            fg_color=self.macos_colors["frame_bg"],
            border_width=1,
            border_color=self.macos_colors["frame_border"],
            corner_radius=8,
            height=30
        )
        self.status_frame.grid(row=3, column=0, columnspan=2, padx=10, pady=(0, 5), sticky="ew")

        # Configure status frame columns for left, center, right layout
        self.status_frame.grid_columnconfigure(0, weight=1)  # Left section
        self.status_frame.grid_columnconfigure(1, weight=1)  # Center section
        self.status_frame.grid_columnconfigure(2, weight=1)  # Right section

        # Left section: Internet status with improved styling
        self.left_status_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
        self.left_status_frame.grid(row=0, column=0, sticky="w", padx=10, pady=5)

        self.status_indicator = ctk.CTkCanvas(
            self.left_status_frame,
            width=16,
            height=16,
            bg=self.macos_colors["frame_bg"],
            highlightthickness=0
        )
        self.status_indicator.pack(side="left", padx=(0, 8))
        self.status_light = self.status_indicator.create_oval(2, 2, 14, 14, fill=self.macos_colors["success_green"], outline="white", width=1)

        self.status_label = ctk.CTkLabel(
            self.left_status_frame,
            text="Internet connection",
            font=("SF Pro", 12),
            text_color=self.macos_colors["text_secondary"]
        )
        self.status_label.pack(side="left")

        # Center section: Date and time with improved styling
        self.datetime_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
        self.datetime_frame.grid(row=0, column=1, sticky="ns", pady=5)
        self.datetime_label = ctk.CTkLabel(
            self.datetime_frame,
            text="",
            font=("SF Mono", 13),
            text_color=self.macos_colors["success_green"]
        )
        self.datetime_label.pack()

        # Right section: "by vonzki" label with improved styling
        self.vonzk_label = ctk.CTkLabel(
            self.status_frame,
            text="2024 by vonzki",
            font=("SF Pro", 10),
            # text_color=self.macos_colors["text_secondary"]
            text_color="#686464"

        )
        self.vonzk_label.grid(row=0, column=2, sticky="e", padx=10, pady=5)
        self.result_queue = queue.Queue()
        self.after(100, self.check_result_queue)
        self.internet_connected = True
        self.update_datetime()
        self.check_internet_periodically()
        self.update_hardware_info()

        # Force white text color for checkboxes after initialization
        self.after(100, self.force_checkbox_white_text)

        # Start the auto-execute process automatically
        self.after(1000, self.auto_execute)  # Start after 1 second to allow GUI to initialize
        # Start the blinking effect
        self.blink_status = True
        self.blink_internet_status()

    def force_checkbox_white_text(self):
        """Force white text color for all checkboxes"""
        for checkbox in self.checkboxes:
            try:
                # Try different parameter names that might work
                checkbox.configure(text_color_disabled="#FFFFFF")
            except:
                try:
                    # Try accessing the internal text label directly
                    if hasattr(checkbox, '_text_label'):
                        checkbox._text_label.configure(fg="#FFFFFF")
                        checkbox._text_label.configure(text_color="#FFFFFF")
                except:
                    try:
                        # Try the tkinter approach
                        checkbox.configure(fg="#FFFFFF")
                    except Exception as e:
                        print(f"Could not set checkbox text color: {e}")
    def mount_network_share_at_startup(self):
        """Mount the network share at application startup"""
        try:
            # Get network configuration
            mount_point = self.network_config["mount_point"]
            server_address = self.network_config["server_address"]
            share_name = self.network_config["share_name"]
            smb_url = self.network_config["smb_url"]

            # First, check if the share is already mounted
            if os.path.exists(mount_point) and os.path.isdir(mount_point):
                # Check if the mount point is actually mounted and not just an empty directory
                try:
                    # List files in the directory to verify it's mounted
                    os.listdir(mount_point)
                    print(f"Network share {smb_url} is already mounted at {mount_point}.")
                    return True
                except PermissionError:
                    # This is likely an empty mount point
                    print("Mount point exists but permission denied. Will try to remount.")
                except Exception as e:
                    # Any other error, try to mount anyway
                    print(f"Mount point check error: {str(e)}. Will try to remount.")

            # Create the mount point directory if it doesn't exist
            if not os.path.exists(mount_point):
                os.makedirs(mount_point, exist_ok=True)
                print(f"Created mount point directory: {mount_point}")

            # Try multiple mounting methods in sequence
            print(f"Attempting to mount {smb_url} at {mount_point}...")

            # Method 1: Using AppleScript with guest credentials
            try:
                applescript = f'''
                tell application "Finder"
                    try
                        mount volume "{smb_url}"
                        return true
                    on error
                        return false
                    end try
                end tell
                '''
                print("Trying AppleScript mount method...")
                result = subprocess.run(['osascript', '-e', applescript],
                                      capture_output=True, text=True, timeout=15)

                if "true" in result.stdout.lower():
                    print(f"Connected to network share successfully using AppleScript: {smb_url}")
                    return True
                else:
                    print(f"AppleScript mount returned: {result.stdout}")
            except Exception as e:
                print(f"AppleScript mount method failed: {str(e)}")

            # Method 2: Using mount_smbfs command
            try:
                # Unmount first if it exists but is not working
                if os.path.exists(mount_point):
                    try:
                        print(f"Unmounting existing mount point: {mount_point}")
                        subprocess.run(f"umount {mount_point}", shell=True, check=False, timeout=5)
                    except Exception as e:
                        print(f"Unmount error (non-critical): {str(e)}")

                # Try mounting with guest credentials
                print("Trying mount_smbfs method...")
                mount_cmd = f"mount_smbfs //guest@{server_address}/{share_name} {mount_point}"
                subprocess.run(mount_cmd, shell=True, check=True, timeout=10)

                # Verify the mount was successful
                os.listdir(mount_point)  # This will raise an exception if not mounted
                print(f"Connected to network share successfully using mount_smbfs: {smb_url}")
                return True
            except Exception as e:
                print(f"mount_smbfs method failed: {str(e)}")

            # Method 3: Using mount command
            try:
                print("Trying mount -t smbfs method...")
                mount_cmd = f"mount -t smbfs //guest@{server_address}/{share_name} {mount_point}"
                subprocess.run(mount_cmd, shell=True, check=True, timeout=10)

                # Verify the mount was successful
                os.listdir(mount_point)  # This will raise an exception if not mounted
                print(f"Connected to network share successfully using mount command: {smb_url}")
                return True
            except Exception as e:
                print(f"mount command method failed: {str(e)}")

            # Method 4: Using open command as a last resort
            try:
                print("Trying open command method...")
                open_cmd = f"open {smb_url}"
                subprocess.run(open_cmd, shell=True, check=True, timeout=10)

                # Wait a moment for the mount to complete
                time.sleep(2)

                # Verify the mount was successful
                if os.path.exists(mount_point) and os.path.isdir(mount_point):
                    try:
                        os.listdir(mount_point)
                        print(f"Connected to network share successfully using open command: {smb_url}")
                        return True
                    except:
                        pass

                print("Open command executed but mount verification failed")
            except Exception as e:
                print(f"open command method failed: {str(e)}")

            # If we got here, all methods failed
            print(f"All network share mounting methods failed for {smb_url}.")
            return False

        except Exception as e:
            print(f"Could not connect to network share: {str(e)}")
            return False

    def load_icon(self, path, size):
        """Load an icon from the specified path and resize it to the given size"""
        try:
            if os.path.exists(path):
                # Open the image file
                img = Image.open(path)
                # Create a CTkImage with the same image for both light and dark modes
                return ctk.CTkImage(light_image=img, dark_image=img, size=size)
            else:
                print(f"Icon not found: {path}")
                # Try a generic document icon as fallback
                fallback_path = get_resource_path("img/vs_icns/checkADE2.icns")
                if os.path.exists(fallback_path):
                    img = Image.open(fallback_path)
                    return ctk.CTkImage(light_image=img, dark_image=img, size=size)
                else:
                    print(f"Fallback icon not found: {fallback_path}")
                    return None
        except Exception as e:
            print(f"Error loading icon {path}: {str(e)}")
            return None

    def update_datetime(self):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=current_time)
        self.after(1000, self.update_datetime)  # Update every second
    def check_internet_connection(self):
        try:
            # Attempt to connect to a reliable server (Google's DNS)
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return True
        except OSError:
            return False
    def check_internet_periodically(self):
        self.internet_connected = self.check_internet_connection()
        if self.internet_connected:
            self.status_indicator.itemconfig(self.status_light, fill=self.macos_colors["success_green"])
            self.status_label.configure(text="Internet connection active")
        else:
            self.status_indicator.itemconfig(self.status_light, fill=self.macos_colors["error_red"])
            self.status_label.configure(text="No internet connection")

        self.after(5000, self.check_internet_periodically)  # Check every 5 seconds

    def blink_internet_status(self):
        current_color = self.status_indicator.itemcget(self.status_light, "fill")
        if self.blink_status:
            bg_color = self.macos_colors["frame_bg"]
            active_color = self.macos_colors["success_green"] if self.internet_connected else self.macos_colors["error_red"]
            new_color = bg_color if current_color != bg_color else active_color
            self.status_indicator.itemconfig(self.status_light, fill=new_color)
        self.after(500, self.blink_internet_status)  # Blink every 500ms
    def check_internet(self):
        if not self.internet_connected:
            return "No internet connection."
        try:
            subprocess.check_output(["ping", "-c", "1", "-W", "3", "8.8.8.8"])
            return "Internet connection is active."
        except subprocess.CalledProcessError:
            return "No internet connection."
        except subprocess.TimeoutExpired:
            return "Internet connection check timed out."
    def check_apple_time(self):
        """Check time with Apple's time server"""
        if not self.internet_connected:
            return "No internet connection. Cannot check Apple time server."

        try:
            # Run sntp command to get time from Apple's time server - we don't need the output
            subprocess.check_output("sntp time.apple.com", shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)

            # Parse the output to get the current time
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return f"Current time: {current_time}"
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            return f"Error getting time from Apple server: {str(e)}"

    def check_apple_server_connection(self):
        """Check connection to Apple servers using network utility"""
        if not self.internet_connected:
            return "No internet connection. Cannot check Apple server connection."

        # Apple server IPs and domains to check
        apple_servers = [
            "*************",  # Apple's main server
            "************",   # Apple's iCloud server
            "swscan.apple.com",
            "www.apple.com"
        ]

        results = []

        # Check for established connections to Apple servers
        for server in apple_servers:
            try:
                # Try to establish a connection
                if "." in server:  # It's an IP address or domain
                    try:
                        socket.create_connection((server, 443), timeout=3)
                        results.append(f"Connection to Apple server {server} - Success")
                    except:
                        results.append(f"Connection to Apple server {server} - Failed")
                else:
                    results.append(f"Skipping invalid server address: {server}")
            except Exception as e:
                results.append(f"Error checking {server}: {str(e)}")

        return "\n".join(results)
    def run_sudo_command(self, command):
        """Run a command with sudo, using default password first and prompting only if needed"""
        global SUDO_PASSWORD

        # Try running the command with the current password
        full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
        result = self.run_command(full_command)

        # Check if the password was incorrect
        if "is not in the sudoers file" in result or "incorrect password" in result:
            # Default password was incorrect, ask for the correct one
            self.display_result("Default sudo authentication failed. Please enter your password.")

            password_result = get_sudo_password(
                self,
                title="Sudo Password Required",
                message="Authentication failed with default password. Please enter your sudo password:"
            )

            if password_result["cancelled"]:
                return "Operation cancelled by user."

            SUDO_PASSWORD = password_result["password"]

            # Try again with the new password
            full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
            result = self.run_command(full_command)

        return result
    def run_command(self, command):
        try:
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            return output.strip()
        except subprocess.CalledProcessError as e:
            return f"Error: {e.output.strip()}"
        except subprocess.TimeoutExpired:
            return "Error: Command timed out"
    def show_device_enrollment_log(self):
        # Use a more specific predicate and limit to last 15 minutes to avoid timeout
        command = "log show --predicate 'eventMessage contains \"Device Enrollment\"' --info --last 15m --style compact"
        return self.run_sudo_command(command)
    def open_system_preferences(self):
        os.system("open /System/Library/PreferencePanes/Profiles.prefPane")
        return "Opened relevant System Settings sections. Please check the Profiles pane."
    def exit_app(self):
        self.quit()

    def shutdown_system(self):
        """Show shutdown confirmation dialog"""
        command = "osascript -e 'tell app \"System Events\" to display dialog \"Are you sure you want to shut down the system?\" buttons {\"Cancel\", \"Shut Down\"} default button \"Cancel\" with icon caution' -e 'if button returned of result is \"Shut Down\" then tell app \"System Events\" to shut down'"
        self.run_sudo_command(command)
        return "Shutdown dialog displayed"
    def auto_execute(self):
        threading.Thread(target=self._auto_execute_thread, daemon=True).start()
    def _auto_execute_thread(self):
        for i, (text, command) in enumerate(self.buttons):
            try:
                if not self.internet_connected and text not in ["Check Internet", "Open Profiles"]:
                    result = "Skipped due to no internet connection"
                elif callable(command):
                    result = command()
                else:
                    result = self.run_command(command)
                self.result_queue.put(f"{text}:\n{result}")
                self.after(0, lambda idx=i: self.checkboxes[idx].select())
            except Exception as e:
                self.result_queue.put(f"Error executing {text}: {str(e)}")
            time.sleep(1)  # Add a small delay between steps
    def display_result(self, result):
        self.result_text.configure(state="normal")

        # Define tags for different types of messages using macOS colors
        self.result_text.tag_config("success", foreground=self.macos_colors["success_green"])
        self.result_text.tag_config("error", foreground=self.macos_colors["error_red"])
        self.result_text.tag_config("info", foreground=self.macos_colors["warning_orange"])
        self.result_text.tag_config("result", foreground=self.macos_colors["accent_blue"])

        # Split the result into lines
        lines = result.split('\n')

        # First line is usually the command/check name
        if lines and ":" in lines[0]:
            command_parts = lines[0].split(":", 1)
            self.result_text.insert(ctk.END, command_parts[0] + ":\n")

            # The rest is the result - highlight appropriately
            if len(lines) > 1:
                for line in lines[1:]:
                    # Check if this is from the Device Enrollment Log
                    is_device_enrollment_log = "Show Device Enrollment Log:" in lines[0]
                    # Check if this is from the Apple Time check
                    is_apple_time_check = "Check Time on time.apple.com:" in lines[0]

                    # Highlight success messages in green
                    if "Success" in line or ("Connection to Apple server" in line and "Success" in line):
                        self.result_text.insert(ctk.END, line + "\n", "success")
                    # Special handling for Device Enrollment Log entries
                    elif is_device_enrollment_log and "[com.apple.log]" in line and "noninteractively" in line:
                        self.result_text.insert(ctk.END, line + "\n", "success")
                    # Highlight error messages in red
                    elif "Error" in line or "Failed" in line or "No internet connection" in line:
                        self.result_text.insert(ctk.END, line + "\n", "error")
                    # Highlight specific result lines in blue
                    elif (line.startswith("DEP Status:") or
                          line.strip() == "No" or
                          "No profiles installed" in line or
                          "Enrolled via DEP:" in line or
                          "MDM enrollment:" in line or
                          line.strip() == "None" or
                          (is_apple_time_check and line.startswith("Current time"))):
                        self.result_text.insert(ctk.END, line + "\n", "result")
                    # Special handling for Device Enrollment Log with specific text highlighting
                    elif "No Device Enrollment configuration was found for this computer" in line:
                        # Split the line at the specific text to highlight only that part
                        parts = line.split("No Device Enrollment configuration was found for this computer")
                        self.result_text.insert(ctk.END, parts[0])
                        self.result_text.insert(ctk.END, "No Device Enrollment configuration was found for this computer", "result")
                        if len(parts) > 1:
                            self.result_text.insert(ctk.END, parts[1] + "\n")
                        else:
                            self.result_text.insert(ctk.END, "\n")
                    # Other informational messages in yellow
                    elif "Please" in line or "ensure" in line or "If you've recently" in line:
                        self.result_text.insert(ctk.END, line + "\n", "info")
                    # Everything else in normal text
                    else:
                        self.result_text.insert(ctk.END, line + "\n")
            elif len(command_parts) > 1:
                # If there's only one line with a result after the colon
                self.result_text.insert(ctk.END, command_parts[1] + "\n", "result")
        else:
            # If there's no command/result structure, just insert the text
            for line in lines:
                self.result_text.insert(ctk.END, line + "\n")

        self.result_text.insert(ctk.END, "\n")
        self.result_text.see(ctk.END)
        self.result_text.configure(state="disabled")
        self.update_idletasks()  # Force GUI update
    def check_result_queue(self):
        try:
            while True:
                result = self.result_queue.get_nowait()
                self.after(0, lambda r=result: self.display_result(r))
        except queue.Empty:
            pass
        finally:
            self.after(100, self.check_result_queue)
    def check_activation_lock_status(self):
        command = "system_profiler SPHardwareDataType | grep 'Activation Lock Status'"
        try:
            output = subprocess.check_output(command, shell=True, text=True)
            return f"Activation Lock Status:\n{output.strip()}"
        except subprocess.CalledProcessError:
            return "Failed to check Activation Lock Status"
    def update_hardware_info(self):
        info = self.get_hardware_info()
        for key, label in self.hardware_info.items():
            if key in info:
                if key == "Activation Lock Status":
                    # Highlight Activation Lock status with appropriate macOS colors
                    if info[key].lower() == "enabled":
                        label.configure(text=info[key], text_color=self.macos_colors["Indian_red"])
                    elif info[key].lower() == "disabled":
                        label.configure(text=info[key], text_color=self.macos_colors["success_green"])
                    else:
                        label.configure(text=info[key], text_color=self.macos_colors["text_secondary"])
                else:
                    label.configure(text=info[key])
        # Force update of the GUI
        self.update_idletasks()
    def get_hardware_info(self):
        commands = {
            "Model Identifier": "sysctl -n hw.model",
            "Chip": "sysctl -n machdep.cpu.brand_string",
            "Memory": "sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'",
            "SSD Storage": "diskutil info / | grep 'Disk Size' | awk '{size=$3; if (size < 64) print \"64GB\"; else if (size < 128) print \"128GB\"; else if (size < 256) print \"256GB\"; else if (size < 512) print \"512GB\"; else if (size < 1024) print \"1TB\"; else print \"2TB\"}'",
            "Serial number": "ioreg -c IOPlatformExpertDevice -d 2 | grep -i 'IOPlatformSerialNumber' | awk -F '\"' '{print $4}'",
            "macOS Version": "sw_vers -productVersion",
            "Activation Lock Status": "system_profiler SPHardwareDataType | grep -i 'Activation Lock' | awk -F ': ' '{print $2}'"
        }
        info = {}
        for key, command in commands.items():
            try:
                output = subprocess.check_output(command, shell=True, text=True).strip()
                info[key] = output.strip('"')  # Remove any quotation marks
            except subprocess.CalledProcessError as e:
                info[key] = "Unable to retrieve"
        return info

    def check_dep_enrollment(self):
        """Check DEP enrollment with better error handling"""
        try:
            # First try to renew enrollment
            command = "sudo profiles renew -type enrollment"
            result = self.run_sudo_command(command)

            # Check for common error messages
            if "No Device Enrollment configuration was found" in result:
                return "DEP Status: Not enrolled in DEP. This device is not configured for Device Enrollment Program."
            elif "Error" in result:
                # If there's an error, try to get the status instead
                status_command = "sudo profiles status -type enrollment"
                status_result = self.run_sudo_command(status_command)

                if "Enrolled via DEP" in status_result:
                    return "DEP Status: Enrolled in DEP."
                elif "No enrollment" in status_result:
                    return "DEP Status: Not enrolled in DEP."
                else:
                    return f"DEP Status: {status_result}"
            else:
                return f"DEP Status: {result}"
        except Exception as e:
            return f"Error checking DEP enrollment: {str(e)}"

    def open_findmy(self):
        """Open FindMy application"""
        try:
            # Try different possible names for the FindMy app
            for app_name in ["FindMy", "Find My", "FindMy.app", "Find My Mac"]:
                result = os.system(f"open -a '{app_name}'")
                if result == 0:  # Command succeeded
                    return f"Opened {app_name} application."

            # If none of the above worked, try opening it via URL scheme
            os.system("open x-apple.findmy://")
            return "Attempted to open FindMy application via URL scheme."
        except Exception as e:
            return f"Error opening FindMy: {str(e)}"
    def save_hardware_overview(self):
        """Save hardware information to CSV file"""
        info = self.get_hardware_info()

        # For macOS, we need to connect to the network share before opening the save dialog
        mount_point = self.network_config["mount_point"]
        server_address = self.network_config["server_address"]
        share_name = self.network_config["share_name"]
        smb_url = self.network_config["smb_url"]

        # Function to mount the network share
        def mount_network_share():
            try:
                # First, check if the share is already mounted
                if os.path.exists(mount_point) and os.path.isdir(mount_point):
                    # Check if the mount point is actually mounted and not just an empty directory
                    try:
                        # List files in the directory to verify it's mounted
                        os.listdir(mount_point)
                        self.display_result(f"Network share {smb_url} is already mounted at {mount_point}.")
                        return True
                    except PermissionError:
                        # This is likely an empty mount point
                        self.display_result("Mount point exists but permission denied. Will try to remount.")
                    except Exception as e:
                        # Any other error, try to mount anyway
                        self.display_result(f"Mount point check error: {str(e)}. Will try to remount.")

                # Create the mount point directory if it doesn't exist
                if not os.path.exists(mount_point):
                    os.makedirs(mount_point, exist_ok=True)
                    self.display_result(f"Created mount point directory: {mount_point}")

                self.display_result(f"Connecting to network share {smb_url}...")

                # Try multiple mounting methods in sequence

                # Method 1: Using AppleScript with guest credentials
                try:
                    applescript = f'''
                    tell application "Finder"
                        try
                            mount volume "{smb_url}"
                            return true
                        on error
                            return false
                        end try
                    end tell
                    '''
                    self.display_result("Trying AppleScript mount method...")
                    result = subprocess.run(['osascript', '-e', applescript],
                                          capture_output=True, text=True, timeout=15)

                    if "true" in result.stdout.lower():
                        self.display_result(f"Connected to network share successfully using AppleScript: {smb_url}")
                        return True
                    else:
                        self.display_result(f"AppleScript mount returned: {result.stdout}")
                except Exception as e:
                    self.display_result(f"AppleScript mount method failed: {str(e)}")

                # Method 2: Using mount_smbfs command
                try:
                    # Unmount first if it exists but is not working
                    if os.path.exists(mount_point):
                        try:
                            self.display_result(f"Unmounting existing mount point: {mount_point}")
                            subprocess.run(f"umount {mount_point}", shell=True, check=False, timeout=5)
                        except Exception as e:
                            self.display_result(f"Unmount error (non-critical): {str(e)}")

                    # Try mounting with guest credentials
                    self.display_result("Trying mount_smbfs method...")
                    mount_cmd = f"mount_smbfs //guest@{server_address}/{share_name} {mount_point}"
                    subprocess.run(mount_cmd, shell=True, check=True, timeout=10)

                    # Verify the mount was successful
                    os.listdir(mount_point)  # This will raise an exception if not mounted
                    self.display_result(f"Connected to network share successfully using mount_smbfs: {smb_url}")
                    return True
                except Exception as e:
                    self.display_result(f"mount_smbfs method failed: {str(e)}")

                # Method 3: Using mount command
                try:
                    self.display_result("Trying mount -t smbfs method...")
                    mount_cmd = f"mount -t smbfs //guest@{server_address}/{share_name} {mount_point}"
                    subprocess.run(mount_cmd, shell=True, check=True, timeout=10)

                    # Verify the mount was successful
                    os.listdir(mount_point)  # This will raise an exception if not mounted
                    self.display_result(f"Connected to network share successfully using mount command: {smb_url}")
                    return True
                except Exception as e:
                    self.display_result(f"mount command method failed: {str(e)}")

                # Method 4: Using open command as a last resort
                try:
                    self.display_result("Trying open command method...")
                    open_cmd = f"open {smb_url}"
                    subprocess.run(open_cmd, shell=True, check=True, timeout=10)

                    # Wait a moment for the mount to complete
                    time.sleep(2)

                    # Verify the mount was successful
                    if os.path.exists(mount_point) and os.path.isdir(mount_point):
                        try:
                            os.listdir(mount_point)
                            self.display_result(f"Connected to network share successfully using open command: {smb_url}")
                            return True
                        except:
                            pass

                    self.display_result("Open command executed but mount verification failed")
                except Exception as e:
                    self.display_result(f"open command method failed: {str(e)}")

                # If we got here, all methods failed
                self.display_result(f"All network share mounting methods failed for {smb_url}.")
                return False

            except Exception as e:
                self.display_result(f"Could not connect to network share: {str(e)}")
                return False

        # Try to mount the share
        mount_successful = mount_network_share()

        # Set the initial directory based on mount success
        if mount_successful and os.path.exists(mount_point) and os.path.isdir(mount_point):
            try:
                # Verify we can actually access the directory
                os.listdir(mount_point)
                initialdir = mount_point
                self.display_result(f"Using network share {smb_url} for saving files.")
            except (PermissionError, OSError) as e:
                # If we can't access it, fall back to desktop
                initialdir = os.path.expanduser("~/Desktop")
                self.display_result(f"Network share mounted but not accessible: {str(e)}. Using desktop for saving files.")
        else:
            # Fall back to desktop if mounting failed
            initialdir = os.path.expanduser("~/Desktop")
            self.display_result("Using desktop for saving files (network share not available).")

        # Get current date for filename
        current_date = datetime.now().strftime("%Y%m%d")
        suggested_filename = f"System_Info_{current_date}.csv"

        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")],
            initialdir=initialdir,
            initialfile=suggested_filename
        )

        if file_path:
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(["Property", "Value"])
                for key, value in info.items():
                    writer.writerow([key, value])
            self.display_result(f"System Information saved to {file_path}")
        else:
            self.display_result("Save operation cancelled")

    def create_xml_info_dialog(self):
        """Create a dialog to collect additional information for XML export"""
        # Create a new dialog window
        dialog = ctk.CTkToplevel(self)
        dialog.title("XML Custom Field Information")
        dialog.geometry("400x330")  # Reduced height from 320 to 300
        dialog.resizable(False,False)
        dialog.grab_set()  # Make the dialog modal

        # Set dark theme
        dialog.configure(fg_color="#1E1E1E")

        # Create form fields
        ctk.CTkLabel(dialog, text="Please enter the following information:", font=("SF Pro", 14, "bold")).pack(pady=(5, 0))

        # Add note about required fields
        # Create a frame for the required note to hold multiple elements
        required_note_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        required_note_frame.pack(pady=(0, 5))

        # Add the text in parts to allow different colors
        ctk.CTkLabel(required_note_frame, text="Fields marked with ", font=("SF Pro", 12), text_color="#999999").pack(side="left")
        ctk.CTkLabel(required_note_frame, text="*", font=("SF Pro", 12, "bold"), text_color="#CD5C5C").pack(side="left")
        ctk.CTkLabel(required_note_frame, text=" are required", font=("SF Pro", 12), text_color="#999999").pack(side="left")

        # Create a frame for the form fields - adjust the bottom padding
        form_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        form_frame.pack(fill="both", expand=False, padx=20, pady=(2, 0))  # Changed expand to False and bottom padding to 0

        # Configure columns for the form frame
        form_frame.grid_columnconfigure(0, weight=0)  # Label column
        form_frame.grid_columnconfigure(1, weight=0)  # Asterisk column
        form_frame.grid_columnconfigure(2, weight=1)  # Entry column
        form_frame.grid_columnconfigure(3, weight=0)  # Error message column

        # Create validation message labels (initially empty)
        tp_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        load_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        usertag_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))

        # TP Number field with required asterisk
        ctk.CTkLabel(form_frame, text="TP Number:", anchor="w").grid(row=0, column=0, padx=(10, 2), pady=5, sticky="w")  # Reduced from 5 to 2
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=0, column=1, padx=(0, 5), pady=5, sticky="w")  # Reduced from 5 to 2
        tp_number_var = ctk.StringVar()
        tp_entry = ctk.CTkEntry(form_frame, textvariable=tp_number_var, width=200)
        tp_entry.grid(row=0, column=2, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2
        tp_validation.grid(row=0, column=3, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2

        # Load Number field with required asterisk
        ctk.CTkLabel(form_frame, text="Load number:", anchor="w").grid(row=1, column=0, padx=(10, 2), pady=5, sticky="w")  # Reduced from 5 to 2
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=1, column=1, padx=(0, 5), pady=5, sticky="w")  # Reduced from 5 to 2
        load_number_var = ctk.StringVar()
        load_entry = ctk.CTkEntry(form_frame, textvariable=load_number_var, width=200)
        load_entry.grid(row=1, column=2, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2
        load_validation.grid(row=1, column=3, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2

        # Usertag field with required asterisk
        ctk.CTkLabel(form_frame, text="Usertag:", anchor="w").grid(row=2, column=0, padx=(10, 2), pady=5, sticky="w")  # Reduced from 5 to 2
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=2, column=1, padx=(0, 5), pady=5, sticky="w")  # Reduced from 5 to 2
        # Default to current user, but leave blank if it's root
        current_user = os.getlogin()
        default_usertag = "" if current_user == "root" else current_user
        usertag_var = ctk.StringVar(value=default_usertag)
        usertag_entry = ctk.CTkEntry(form_frame, textvariable=usertag_var, width=200)
        usertag_entry.grid(row=2, column=2, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2
        usertag_validation.grid(row=2, column=3, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2

        # Client Asset Number field (not required)
        ctk.CTkLabel(form_frame, text="Client Asset Number:", anchor="w").grid(row=3, column=0, padx=(10, 2), pady=5, sticky="w")  # Reduced from 5 to 2
        ctk.CTkLabel(form_frame, text="", font=("SF Pro", 14)).grid(row=3, column=1, padx=(0, 5), pady=5, sticky="w")  # Reduced from 5 to 2
        asset_number_var = ctk.StringVar()
        asset_entry = ctk.CTkEntry(form_frame, textvariable=asset_number_var, width=200)
        asset_entry.grid(row=3, column=2, padx=5, pady=5, sticky="w")  # Reduced from 5 to 2

        # Comment field (not required)
        ctk.CTkLabel(form_frame, text="Comment:", anchor="w").grid(row=4, column=0, padx=(10, 2), pady=5, sticky="w")  # Reduced from 5 to 2
        ctk.CTkLabel(form_frame, text="", font=("SF Pro", 14)).grid(row=4, column=1, padx=(0, 5), pady=10, sticky="w")  # Reduced from 5 to 2
        comment_var = ctk.StringVar()
        comment_entry = ctk.CTkEntry(form_frame, textvariable=comment_var, width=200)
        comment_entry.grid(row=4, column=2, padx=5, pady=10, sticky="w")  # Reduced from 5 to 2

        # Create a frame for buttons with minimal padding
        button_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        button_frame.pack(fill="x", padx=20, pady=(10, 5))  # Top padding set to 0

        # Dictionary to store the results
        result = {"confirmed": False, "data": {}}

        # Function to clear all validation messages
        def clear_validation_messages():
            tp_validation.configure(text="")
            load_validation.configure(text="")
            usertag_validation.configure(text="")

        # Function to handle OK button click
        def on_ok():
            # Clear any previous validation messages
            clear_validation_messages()

            # Check if required fields are filled
            tp_number = tp_number_var.get().strip()
            load_number = load_number_var.get().strip()
            usertag = usertag_var.get().strip()

            # Flag to track if validation passed
            validation_passed = True

            if not tp_number:
                tp_validation.configure(text="Required")
                tp_entry.focus_set()
                validation_passed = False

            if not load_number:
                load_validation.configure(text="Required")
                if validation_passed:  # Only set focus if no previous field had an error
                    load_entry.focus_set()
                validation_passed = False

            if not usertag:
                usertag_validation.configure(text="Required")
                if validation_passed:  # Only set focus if no previous field had an error
                    usertag_entry.focus_set()
                validation_passed = False

            # If validation failed, return without closing the dialog
            if not validation_passed:
                return

            # All validation passed, save the data and close the dialog
            result["confirmed"] = True
            result["data"] = {
                "tp_number": tp_number,
                "load_number": load_number,
                "usertag": usertag,
                "asset_number": asset_number_var.get().strip(),
                "comment": comment_var.get().strip()
            }
            dialog.destroy()

        # Function to handle Cancel button click
        def on_cancel():
            dialog.destroy()

        # Add OK and Cancel buttons
        ctk.CTkButton(button_frame, text="OK", command=on_ok,
                     fg_color="#007ACC", hover_color="#005999").pack(side="right", padx=20)
        ctk.CTkButton(button_frame, text="Cancel", command=on_cancel,
                     fg_color="#333333", hover_color="#444444").pack(side="right", padx=5)

        # Set focus to the first field
        tp_entry.focus_set()

        # Wait for the dialog to be closed
        self.wait_window(dialog)

        return result

    def save_hardware_to_xml(self):
        """Save hardware information to XML file compatible with Blancco Management Console"""
        # Get user input from dialog
        dialog_result = self.create_xml_info_dialog()

        if not dialog_result["confirmed"]:
            self.display_result("XML export cancelled")
            return

        # Get form data
        form_data = dialog_result["data"]
        tp_number = form_data["tp_number"]
        load_number = form_data["load_number"]
        usertag = form_data["usertag"]
        asset_number = form_data["asset_number"]
        comment = form_data["comment"]

        # Create document_id from TP number and usertag in the format "TPnumber@usertag"
        document_id = f"{tp_number}@{usertag}"

        # Get hardware info
        info = self.get_hardware_info()

        # Ask for save location
        # Use TP number as the filename if available, otherwise use load number
        suggested_filename = f"{tp_number}.xml" if tp_number else (f"{load_number}.xml" if load_number else "export.xml")

        # For macOS, we need to connect to the network share before opening the save dialog
        mount_point = self.network_config["mount_point"]
        server_address = self.network_config["server_address"]
        share_name = self.network_config["share_name"]
        smb_url = self.network_config["smb_url"]

        # Function to mount the network share
        def mount_network_share():
            try:
                # First, check if the share is already mounted
                if os.path.exists(mount_point) and os.path.isdir(mount_point):
                    # Check if the mount point is actually mounted and not just an empty directory
                    try:
                        # List files in the directory to verify it's mounted
                        os.listdir(mount_point)
                        self.display_result(f"Network share {smb_url} is already mounted at {mount_point}.")
                        return True
                    except PermissionError:
                        # This is likely an empty mount point
                        self.display_result("Mount point exists but permission denied. Will try to remount.")
                    except Exception as e:
                        # Any other error, try to mount anyway
                        self.display_result(f"Mount point check error: {str(e)}. Will try to remount.")

                # Create the mount point directory if it doesn't exist
                if not os.path.exists(mount_point):
                    os.makedirs(mount_point, exist_ok=True)
                    self.display_result(f"Created mount point directory: {mount_point}")

                self.display_result(f"Connecting to network share {smb_url}...")

                # Try multiple mounting methods in sequence

                # Method 1: Using AppleScript with guest credentials
                try:
                    applescript = f'''
                    tell application "Finder"
                        try
                            mount volume "{smb_url}"
                            return true
                        on error
                            return false
                        end try
                    end tell
                    '''
                    self.display_result("Trying AppleScript mount method...")
                    result = subprocess.run(['osascript', '-e', applescript],
                                          capture_output=True, text=True, timeout=15)

                    if "true" in result.stdout.lower():
                        self.display_result(f"Connected to network share successfully using AppleScript: {smb_url}")
                        return True
                    else:
                        self.display_result(f"AppleScript mount returned: {result.stdout}")
                except Exception as e:
                    self.display_result(f"AppleScript mount method failed: {str(e)}")

                # Method 2: Using mount_smbfs command
                try:
                    # Unmount first if it exists but is not working
                    if os.path.exists(mount_point):
                        try:
                            self.display_result(f"Unmounting existing mount point: {mount_point}")
                            subprocess.run(f"umount {mount_point}", shell=True, check=False, timeout=5)
                        except Exception as e:
                            self.display_result(f"Unmount error (non-critical): {str(e)}")

                    # Try mounting with guest credentials
                    self.display_result("Trying mount_smbfs method...")
                    mount_cmd = f"mount_smbfs //guest@{server_address}/{share_name} {mount_point}"
                    subprocess.run(mount_cmd, shell=True, check=True, timeout=10)

                    # Verify the mount was successful
                    os.listdir(mount_point)  # This will raise an exception if not mounted
                    self.display_result(f"Connected to network share successfully using mount_smbfs: {smb_url}")
                    return True
                except Exception as e:
                    self.display_result(f"mount_smbfs method failed: {str(e)}")

                # Method 3: Using mount command
                try:
                    self.display_result("Trying mount -t smbfs method...")
                    mount_cmd = f"mount -t smbfs //guest@{server_address}/{share_name} {mount_point}"
                    subprocess.run(mount_cmd, shell=True, check=True, timeout=10)

                    # Verify the mount was successful
                    os.listdir(mount_point)  # This will raise an exception if not mounted
                    self.display_result(f"Connected to network share successfully using mount command: {smb_url}")
                    return True
                except Exception as e:
                    self.display_result(f"mount command method failed: {str(e)}")

                # Method 4: Using open command as a last resort
                try:
                    self.display_result("Trying open command method...")
                    open_cmd = f"open {smb_url}"
                    subprocess.run(open_cmd, shell=True, check=True, timeout=10)

                    # Wait a moment for the mount to complete
                    time.sleep(2)

                    # Verify the mount was successful
                    if os.path.exists(mount_point) and os.path.isdir(mount_point):
                        try:
                            os.listdir(mount_point)
                            self.display_result(f"Connected to network share successfully using open command: {smb_url}")
                            return True
                        except:
                            pass

                    self.display_result("Open command executed but mount verification failed")
                except Exception as e:
                    self.display_result(f"open command method failed: {str(e)}")

                # If we got here, all methods failed
                self.display_result(f"All network share mounting methods failed for {smb_url}.")
                return False

            except Exception as e:
                self.display_result(f"Could not connect to network share: {str(e)}")
                return False

        # Try to mount the share
        mount_successful = mount_network_share()

        # Set the initial directory based on mount success
        if mount_successful and os.path.exists(mount_point) and os.path.isdir(mount_point):
            try:
                # Verify we can actually access the directory
                os.listdir(mount_point)
                initialdir = mount_point
                self.display_result(f"Using network share {smb_url} for saving files.")
            except (PermissionError, OSError) as e:
                # If we can't access it, fall back to desktop
                initialdir = os.path.expanduser("~/Desktop")
                self.display_result(f"Network share mounted but not accessible: {str(e)}. Using desktop for saving files.")
        else:
            # Fall back to desktop if mounting failed
            initialdir = os.path.expanduser("~/Desktop")
            self.display_result("Using desktop for saving files (network share not available).")

        file_path = filedialog.asksaveasfilename(
            defaultextension=".xml",
            filetypes=[("XML files", "*.xml")],
            initialfile=suggested_filename,
            initialdir=initialdir
        )

        if not file_path:
            self.display_result("Save operation cancelled")
            return

        # Create XML structure based on blancco_report.xml format (working format)
        root = ET.Element("root")
        report = ET.SubElement(root, "report")

        # Create blancco_data section
        blancco_data = ET.SubElement(report, "blancco_data")

        # Create description section
        description = ET.SubElement(blancco_data, "description")
        ET.SubElement(description, "document_id").text = document_id

        # Create document_log section
        document_log = ET.SubElement(description, "document_log")

        # Add log entry
        log_entry = ET.SubElement(document_log, "log_entry")
        author = ET.SubElement(log_entry, "author")
        product_name = ET.SubElement(author, "product_name", id="51", name="vs mactools")
        product_name.text = "Blancco Management Console"
        ET.SubElement(author, "product_version").text = "2.0.1"
        ET.SubElement(author, "product_revision").text = "N/A"

        # Add current date/time in ISO format with timezone
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S+0900")
        ET.SubElement(log_entry, "date").text = current_time
        ET.SubElement(log_entry, "integrity").text = "Not applicable, user created"
        ET.SubElement(log_entry, "key")

        # Add description entries
        description_entries = ET.SubElement(description, "entry", name="description_entries")
        ET.SubElement(description_entries, "entry", name="verified", type="string").text = "false"
        ET.SubElement(description_entries, "entry", name="verified", type="string").text = "false"
        ET.SubElement(description_entries, "entry", name="verified", type="string").text = "true"

        # Create hardware report section
        hardware_report = ET.SubElement(blancco_data, "blancco_hardware_report")
        system_entries = ET.SubElement(hardware_report, "entries", name="system")

        # Add manufacturer entry
        ET.SubElement(system_entries, "entry", name="manufacturer", type="string").text = "Apple Inc."

        # Map our hardware info keys to the XML format keys
        key_mapping = {
            "Model Identifier": "model",
            "Serial number": "serial"
        }

        # Add hardware info entries
        for key, value in info.items():
            if key in key_mapping:
                ET.SubElement(system_entries, "entry", name=key_mapping[key], type="string").text = value

        # Add chassis type
        ET.SubElement(system_entries, "entry", name="chassis_type", type="string").text = "Notebook"

        # Add RAM information (NEW)
        memory_entries = ET.SubElement(hardware_report, "entries", name="memory")
        memory_device = ET.SubElement(memory_entries, "entries", name="memory_device")

        # Memory size
        memory_str = info.get("Memory", "8GB")
        memory_gb = int(memory_str.replace("GB", "").strip())
        memory_bytes = memory_gb * 1024 * 1024 * 1024
        ET.SubElement(memory_device, "entry", name="size", type="uint").text = str(memory_bytes)
        ET.SubElement(memory_device, "entry", name="type", type="string").text = "DDR4"

        # Add Processor information (NEW)
        processor_entries = ET.SubElement(hardware_report, "entries", name="processors")
        processor_device = ET.SubElement(processor_entries, "entries", name="processor")

        # Processor details based on chip info
        chip_info = info.get("Chip", "Apple M1")
        if "M1" in chip_info:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Apple Inc."
            ET.SubElement(processor_device, "entry", name="model", type="string").text = "Apple M1"
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "8"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "3200"
        elif "M2" in chip_info:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Apple Inc."
            ET.SubElement(processor_device, "entry", name="model", type="string").text = "Apple M2"
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "8"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "3500"
        elif "M3" in chip_info:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Apple Inc."
            ET.SubElement(processor_device, "entry", name="model", type="string").text = "Apple M3"
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "8"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "4000"
        elif "Intel" in chip_info:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Intel Corporation"
            ET.SubElement(processor_device, "entry", name="model", type="string").text = chip_info
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "4"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "2800"
        else:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Apple Inc."
            ET.SubElement(processor_device, "entry", name="model", type="string").text = chip_info
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "8"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "3200"

        # Add disk information (existing format)
        disks_entries = ET.SubElement(hardware_report, "entries", name="disks")
        disk_entry = ET.SubElement(disks_entries, "entries", name="disk")
        ET.SubElement(disk_entry, "entry", name="id", type="uint").text = "34"
        ET.SubElement(disk_entry, "entry", name="index", type="uint").text = "1"
        ET.SubElement(disk_entry, "entry", name="model", type="string").text = info.get("Model Identifier", "Unknown")
        ET.SubElement(disk_entry, "entry", name="vendor", type="string").text = "Apple"
        ET.SubElement(disk_entry, "entry", name="serial", type="string").text = info.get("Serial number", "Unknown")
        ET.SubElement(disk_entry, "entry", name="interface_type", type="string").text = "NVMe"

        # Get storage capacity
        storage_str = info.get("SSD Storage", "512GB")
        # Convert storage to bytes (approximate)
        if "TB" in storage_str:
            capacity = int(float(storage_str.replace("TB", "").strip()) * 1000000000000)
        else:
            capacity = int(float(storage_str.replace("GB", "").strip()) * 1000000000)
        ET.SubElement(disk_entry, "entry", name="capacity", type="uint").text = str(capacity)

        # Create user data section (original working format)
        user_data = ET.SubElement(report, "user_data")
        fields = ET.SubElement(user_data, "entries", name="fields")
        ET.SubElement(fields, "entry", name="Load_number", type="string").text = load_number
        ET.SubElement(fields, "entry", name="TP_number", type="string").text = tp_number
        ET.SubElement(fields, "entry", name="Usertag", type="string").text = usertag if usertag else ""
        ET.SubElement(fields, "entry", name="Client_Asset_number", type="string").text = asset_number
        ET.SubElement(fields, "entry", name="Comment", type="string").text = comment

        # Convert to XML string with proper formatting
        xml_str = ET.tostring(root, encoding='utf-8')

        # Parse and prettify the XML
        dom = xml.dom.minidom.parseString(xml_str)
        pretty_xml = dom.toprettyxml(indent="  ")

        # Remove empty lines and fix formatting
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        formatted_xml = '\n'.join(lines)

        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_xml)

        self.display_result(f"System Information saved to XML file: {file_path}")
    def open_bluetooth_settings(self):
        os.system("open /System/Library/PreferencePanes/Bluetooth.prefPane")
    def show_system_info(self):
        os.system("open -a 'System Information'")
    def erase_app(self):
        """Open the Erase Assistant app"""
        # The path to Erase Assistant varies by macOS version
        if MACOS_VERSION >= (11, 0, 0):  # Big Sur or later
            command = "open /System/Library/CoreServices/Erase\\ Assistant.app"
        else:  # Catalina or earlier
            command = "open -a 'System Preferences' /System/Library/PreferencePanes/ResetReset.prefPane"

        result = self.run_command(command)
        return f"Opened Erase Assistant: {result}"
    def delete_bluetooth_devices(self):
        """Delete all paired Bluetooth devices"""
        # First, check and display paired devices
        devices_info = self.check_bluetooth_devices()
        self.display_result(f"Current Bluetooth devices:\n{devices_info}")

        # Commands to remove Bluetooth devices
        commands = [
            "sudo rm -rf /Library/Preferences/com.apple.Bluetooth.plist",
            "sudo defaults delete /Library/Preferences/com.apple.Bluetooth.plist DeviceCache",
            "sudo defaults delete /Library/Preferences/com.apple.Bluetooth.plist PairedDevices",
            "sudo defaults delete ~/Library/Preferences/com.apple.Bluetooth.plist"
        ]

        results = []
        for cmd in commands:
            try:
                result = self.run_sudo_command(cmd)
                results.append(result)
            except Exception as e:
                results.append(f"Error: {str(e)}")

        # Restart Bluetooth service
        restart_cmd = "sudo pkill -HUP blued"
        try:
            restart_result = self.run_sudo_command(restart_cmd)
            results.append(restart_result)
        except Exception as e:
            results.append(f"Error restarting Bluetooth: {str(e)}")

        # Return a summary of the results
        return "Deleted all registered Bluetooth devices. Please restart your computer to complete the process."

    def check_findmy_status(self):
        """Check if FindMy is enabled on the device"""
        try:
            # First check if FindMy is enabled via iCloud
            command = "defaults read ~/Library/Preferences/MobileMeAccounts.plist Accounts | grep -A 5 -B 5 'FindMyMac'"
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)

            if "FindMyMac = 1" in output:
                return "FindMy is ENABLED on this device."
            elif "FindMyMac = 0" in output:
                return "FindMy is DISABLED on this device."
            else:
                # If the first check doesn't give clear results, try an alternate method
                command2 = "defaults read /Library/Preferences/com.apple.FindMyMac.plist FMMEnabled"
                output2 = subprocess.check_output(command2, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)

                if "1" in output2:
                    return "FindMy is ENABLED on this device (alternate check)."
                else:
                    return "FindMy is DISABLED on this device (alternate check)."
        except subprocess.CalledProcessError:
            # If both methods fail, try a third method
            try:
                command3 = "sudo profiles show | grep -i 'find my'"
                output3 = self.run_sudo_command(command3)

                if "Find My Mac" in output3:
                    return "FindMy appears to be configured on this device."
                else:
                    return "FindMy status could not be determined with certainty. It may be DISABLED."
            except:
                return "Failed to check FindMy status."
        except subprocess.TimeoutExpired:
            return "FindMy status check timed out."

    def check_bluetooth_devices(self):
        """Check for paired Bluetooth devices"""
        try:
            # Use system_profiler to get Bluetooth information
            command = "system_profiler SPBluetoothDataType"
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)

            # Parse the output to find paired devices
            lines = output.split('\n')
            devices = []
            current_device = None

            for line in lines:
                if "Paired:" in line and "Yes" in line:
                    if current_device:
                        devices.append(current_device)
                elif "Connected:" in line and current_device:
                    status = "Connected" if "Yes" in line else "Not Connected"
                    devices.append(f"{current_device} - {status}")
                    current_device = None
                elif ":" in line and not line.strip().startswith("Paired") and not line.strip().startswith("Connected"):
                    current_device = line.strip().rstrip(':')

            if devices:
                return f"Found {len(devices)} paired Bluetooth devices:\n" + "\n".join(devices)
            else:
                return "No paired Bluetooth devices found."
        except subprocess.CalledProcessError:
            return "Failed to check Bluetooth devices."
        except subprocess.TimeoutExpired:
            return "Bluetooth devices check timed out."

    def check_profiles_installed(self):
        """Check for installed profiles"""
        try:
            # Use profiles command to list all profiles
            command = "profiles show -all"
            output = self.run_sudo_command(command)

            if not output or "Error" in output:
                # Try alternate command if the first one fails
                command = "profiles list"
                output = self.run_sudo_command(command)

            if not output or "Error" in output:
                return "No profiles found or unable to retrieve profiles information."

            # Parse the output to count and list profiles
            lines = output.split('\n')
            profile_count = 0
            profiles = []

            for line in lines:
                if "_computerlevel" in line or "attribute:" in line:
                    profile_count += 1
                    profile_name = line.strip()
                    profiles.append(profile_name)

            if profile_count > 0:
                return f"Found {profile_count} installed profiles:\n" + "\n".join(profiles)
            else:
                return "No profiles installed on this device."
        except Exception as e:
            return f"Failed to check installed profiles: {str(e)}"

def main():
    """Main entry point for the application"""
    try:
        # Set up error handling for the application
        import traceback

        # Create and run the application
        app = InternetCheckerApp()
        app.mainloop()

    except Exception as e:
        # Log the error and show a user-friendly message
        error_msg = f"Application error: {str(e)}\n\nTraceback:\n{traceback.format_exc()}"
        print(error_msg)

        # Try to show an error dialog if possible
        try:
            import tkinter.messagebox as msgbox
            msgbox.showerror("Application Error",
                           f"An error occurred while running the application:\n\n{str(e)}\n\nPlease check the console for more details.")
        except:
            # If we can't show a dialog, just print the error
            print("Failed to show error dialog")

        # Exit with error code
        sys.exit(1)

if __name__ == "__main__":
    main()
