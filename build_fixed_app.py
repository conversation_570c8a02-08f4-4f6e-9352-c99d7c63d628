#!/usr/bin/env python3
"""
Fixed build script for VS Mac Tools v2 - Addresses crash issues and adds proper icon
"""

import os
import sys
import subprocess
import shutil

def create_app_icon():
    """Convert JPEG to ICNS for app icon"""
    icon_source = "img/vs_mac_tool_v2.jpeg"
    icon_output = "img/vs_mac_tool_v2.icns"
    
    if os.path.exists(icon_source):
        try:
            print(f"🎨 Converting {icon_source} to ICNS...")
            # Use sips to convert JPEG to ICNS (macOS built-in tool)
            subprocess.run([
                'sips', '-s', 'format', 'icns', 
                icon_source, '--out', icon_output
            ], check=True, capture_output=True)
            print(f"✅ Created app icon: {icon_output}")
            return icon_output
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Could not create ICNS from {icon_source}: {e}")
            return None
    else:
        print(f"❌ Icon source {icon_source} not found")
        return None

def clean_build():
    """Clean previous build artifacts"""
    dirs_to_clean = ['build', 'dist']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 Cleaned {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"🧹 Cleaned {file}")

def create_debug_version():
    """Create a debug version to see what's causing the crash"""
    print("🔍 Creating debug version to identify crash cause...")
    
    # Create a simple debug script
    debug_script = """
import sys
import traceback
import os

def debug_main():
    try:
        print("Debug: Starting VS Mac Tools v2...")
        print(f"Debug: Python version: {sys.version}")
        print(f"Debug: Current working directory: {os.getcwd()}")
        print(f"Debug: Python path: {sys.path}")
        
        # Test imports one by one
        print("Debug: Testing imports...")
        
        try:
            import tkinter
            print("Debug: ✅ tkinter imported successfully")
        except Exception as e:
            print(f"Debug: ❌ tkinter import failed: {e}")
            
        try:
            import customtkinter
            print("Debug: ✅ customtkinter imported successfully")
        except Exception as e:
            print(f"Debug: ❌ customtkinter import failed: {e}")
            
        try:
            import PIL
            print("Debug: ✅ PIL imported successfully")
        except Exception as e:
            print(f"Debug: ❌ PIL import failed: {e}")
            
        try:
            import keyring
            print("Debug: ✅ keyring imported successfully")
        except Exception as e:
            print(f"Debug: ❌ keyring import failed: {e}")
        
        # Try to import the main module
        print("Debug: Importing main application...")
        import vs_mac_tools_v2
        print("Debug: ✅ Main module imported successfully")
        
        # Try to create the app
        print("Debug: Creating application instance...")
        app = vs_mac_tools_v2.VSMacToolsApp()
        print("Debug: ✅ Application instance created")
        
        print("Debug: Starting main loop...")
        app.mainloop()
        
    except Exception as e:
        print(f"Debug: ❌ Error occurred: {e}")
        print(f"Debug: Traceback:")
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    debug_main()
"""
    
    with open("debug_vs_mac_tools.py", "w") as f:
        f.write(debug_script)
    
    print("✅ Created debug_vs_mac_tools.py")
    return "debug_vs_mac_tools.py"

def build_fixed_app():
    """Build the macOS app with fixes for crash issues"""
    
    print("🚀 Building VS Mac Tools v2 with crash fixes...")
    
    # Clean previous builds
    clean_build()
    
    # Create app icon
    app_icon = create_app_icon()
    
    # Create debug version first
    debug_script = create_debug_version()
    
    # Enhanced PyInstaller command with more comprehensive options
    cmd = [
        'pyinstaller',
        '--onedir',
        '--windowed',
        '--name=VS Mac Tools v2',
        '--clean',
        '--noconfirm',
        
        # Add all data files
        '--add-data=img:img',
        '--add-data=xml:xml',
        '--add-data=password_dialog.py:.',
        
        # Comprehensive hidden imports
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.simpledialog',
        '--hidden-import=_tkinter',
        '--hidden-import=customtkinter',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL.ImageTk',
        '--hidden-import=PIL._tkinter_finder',
        '--hidden-import=keyring',
        '--hidden-import=keyring.backends',
        '--hidden-import=keyring.backends.macOS',
        '--hidden-import=xml.etree.ElementTree',
        '--hidden-import=xml.dom.minidom',
        '--hidden-import=subprocess',
        '--hidden-import=threading',
        '--hidden-import=queue',
        '--hidden-import=socket',
        '--hidden-import=csv',
        '--hidden-import=datetime',
        '--hidden-import=pathlib',
        '--hidden-import=platform',
        '--hidden-import=getpass',
        '--hidden-import=os',
        '--hidden-import=sys',
        
        # Collect all data for problematic packages
        '--collect-data=customtkinter',
        '--collect-data=tkinter',
        '--collect-all=customtkinter',
        
        # Copy metadata
        '--copy-metadata=customtkinter',
        '--copy-metadata=pillow',
        '--copy-metadata=keyring',
        
        # macOS specific options
        '--osx-bundle-identifier=com.vonzki.vsmactools',
        
        # Add runtime options to help with path issues
        '--runtime-tmpdir=.',
        
        # Main script
        'vs_mac_tools_v2.py'
    ]
    
    # Add icon if available
    if app_icon and os.path.exists(app_icon):
        cmd.extend(['--icon', app_icon])
        print(f"✅ Using app icon: {app_icon}")
    
    print("🔨 Running PyInstaller with comprehensive options...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PyInstaller completed successfully")
        
        # Check if app was created
        app_path = "dist/VS Mac Tools v2.app"
        if os.path.exists(app_path):
            print(f"✅ App created successfully: {app_path}")
            
            # Set executable permissions
            executable_path = f"{app_path}/Contents/MacOS/VS Mac Tools v2"
            if os.path.exists(executable_path):
                os.chmod(executable_path, 0o755)
                print("✅ Set executable permissions")
            
            # Check app size
            size_result = subprocess.run(['du', '-sh', app_path], capture_output=True, text=True)
            if size_result.returncode == 0:
                size = size_result.stdout.strip().split()[0]
                print(f"📦 App size: {size}")
            
            return True
        else:
            print("❌ App was not created")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller failed: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False

def build_debug_app():
    """Build a debug version to help identify crash issues"""
    print("🔍 Building debug version...")
    
    cmd = [
        'pyinstaller',
        '--onedir',
        '--console',  # Show console for debug output
        '--name=VS Mac Tools v2 Debug',
        '--clean',
        '--noconfirm',
        '--add-data=img:img',
        '--add-data=xml:xml',
        '--add-data=password_dialog.py:.',
        '--add-data=vs_mac_tools_v2.py:.',
        '--hidden-import=tkinter',
        '--hidden-import=customtkinter',
        '--hidden-import=PIL',
        '--hidden-import=keyring',
        '--collect-data=customtkinter',
        '--osx-bundle-identifier=com.vonzki.vsmactools.debug',
        'debug_vs_mac_tools.py'
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Debug version created: dist/VS Mac Tools v2 Debug.app")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Debug build failed: {e}")
        return False

def test_app():
    """Test the built app"""
    app_path = "dist/VS Mac Tools v2.app"
    debug_path = "dist/VS Mac Tools v2 Debug.app"
    
    if os.path.exists(debug_path):
        print("🧪 Testing debug version first...")
        try:
            subprocess.run(['open', debug_path], check=True)
            print("✅ Debug app launched - check console output")
            input("Press Enter after checking debug output...")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to launch debug app: {e}")
    
    if os.path.exists(app_path):
        print("🧪 Testing main app...")
        try:
            subprocess.run(['open', app_path], check=True)
            print("✅ Main app launched")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to launch main app: {e}")
            return False
    else:
        print("❌ App not found for testing")
        return False

def main():
    """Main build process"""
    print("=" * 60)
    print("VS Mac Tools v2 - Fixed macOS App Builder")
    print("=" * 60)
    
    # Check if we're on macOS
    if sys.platform != 'darwin':
        print("❌ This script must be run on macOS")
        sys.exit(1)
    
    # Check if main script exists
    if not os.path.exists('vs_mac_tools_v2.py'):
        print("❌ vs_mac_tools_v2.py not found")
        sys.exit(1)
    
    # Build debug version first
    print("🔍 Building debug version to identify issues...")
    if build_debug_app():
        print("✅ Debug version built successfully")
    
    # Build the main app
    if build_fixed_app():
        print("\n" + "=" * 60)
        print("✅ BUILD SUCCESSFUL!")
        print("=" * 60)
        print(f"📱 App location: dist/VS Mac Tools v2.app")
        print(f"🔍 Debug version: dist/VS Mac Tools v2 Debug.app")
        print("🚀 You can now test and distribute this app")
        
        # Ask if user wants to test
        response = input("\n🧪 Do you want to test the app now? (y/n): ")
        if response.lower() in ['y', 'yes']:
            test_app()
    else:
        print("\n" + "=" * 60)
        print("❌ BUILD FAILED!")
        print("=" * 60)
        print("\n💡 Try running the debug version to see what's causing the crash")
        sys.exit(1)

if __name__ == "__main__":
    main()
