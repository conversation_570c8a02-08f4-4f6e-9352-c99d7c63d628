-- AppleScript to launch VS Mac Tools v2
-- This can be saved as an Application in Script Editor

on run
    try
        -- Get the path to the script directory
        set scriptPath to (path to me as text)
        set scriptDir to (do shell script "dirname " & quoted form of POSIX path of scriptPath)
        
        -- Change to the script directory
        do shell script "cd " & quoted form of scriptDir
        
        -- Check if virtual environment exists
        try
            do shell script "test -d " & quoted form of (scriptDir & "/venv")
        on error
            -- Create virtual environment if it doesn't exist
            display dialog "Setting up VS Mac Tools for first run..." buttons {"OK"} default button "OK" with icon note
            do shell script "cd " & quoted form of scriptDir & " && python3 -m venv venv && source venv/bin/activate && pip install --upgrade pip && pip install customtkinter pillow keyring darkdetect"
        end try
        
        -- Run the Python application
        do shell script "cd " & quoted form of scriptDir & " && source venv/bin/activate && python3 vs_mac_tools_v2.py > /dev/null 2>&1 &"
        
    on error errorMessage
        display dialog "Error launching VS Mac Tools: " & errorMessage buttons {"OK"} default button "OK" with icon stop
    end try
end run
