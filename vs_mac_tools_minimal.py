#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

print("🚀 Starting minimal VS Mac Tools...")

try:
    print("1. Testing basic imports...")
    import tkinter as tk
    print("   ✅ tkinter imported")
    
    print("2. Creating basic window...")
    root = tk.Tk()
    root.title("VS Mac Tools v2 - Minimal")
    root.geometry("800x600")
    
    print("   ✅ Window created")
    
    # Create basic UI
    label = tk.Label(root, text="VS Mac Tools v2 - Minimal Version", font=("Arial", 16))
    label.pack(pady=20)
    
    button = tk.Button(root, text="Test Button", command=lambda: print("Button clicked!"))
    button.pack(pady=10)
    
    text_area = tk.Text(root, height=20, width=80)
    text_area.pack(pady=10, padx=20, fill="both", expand=True)
    text_area.insert("1.0", "VS Mac Tools v2 - Minimal Version\n\nThis is a basic version to test functionality.\n")
    
    print("   ✅ UI elements created")
    
    def on_closing():
        print("👋 Closing application...")
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    print("3. Starting main loop...")
    root.mainloop()
    
    print("✅ Application closed successfully")

except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
