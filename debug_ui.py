#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

import customtkinter as ctk

print("🔍 Testing CustomTkinter UI...")

try:
    # Set appearance mode
    ctk.set_appearance_mode("dark")
    
    # Create main window
    app = ctk.CTk()
    app.title("UI Test")
    app.geometry("800x600")
    
    print("✅ Main window created")
    
    # Create a simple frame
    frame = ctk.CTkFrame(app, width=300, height=200)
    frame.pack(padx=20, pady=20)
    
    print("✅ Frame created")
    
    # Create a label
    label = ctk.CTkLabel(frame, text="Hello World!", font=("Arial", 16))
    label.pack(pady=20)
    
    print("✅ Label created")
    
    # Create a button
    button = ctk.CTkButton(frame, text="Test Button")
    button.pack(pady=10)
    
    print("✅ Button created")
    
    # Update the window to make sure everything is rendered
    app.update()
    
    print("✅ Window updated")
    print("🎯 UI should be visible now!")
    print("   Close the window to continue...")
    
    # Run the main loop
    app.mainloop()
    
    print("✅ App closed successfully")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
