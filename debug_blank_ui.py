#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

print("🔍 DEBUG: Testing what's causing blank UI...")

# Test 1: Basic CustomTkinter
print("\n1. Testing basic CustomTkinter...")
try:
    import customtkinter as ctk
    print("   ✅ CustomTkinter imported")
    
    app = ctk.CTk()
    app.title("Test 1: Basic CTk")
    app.geometry("400x300")
    
    label = ctk.CTkLabel(app, text="Test Label", font=("Arial", 16))
    label.pack(pady=20)
    
    print("   ✅ Basic CTk window created")
    app.update()
    print("   ✅ Window updated - should be visible")
    
    # Don't run mainloop, just test creation
    app.destroy()
    print("   ✅ Basic test passed")
    
except Exception as e:
    print(f"   ❌ Basic test failed: {e}")
    import traceback
    traceback.print_exc()

# Test 2: Test the actual VS Mac Tools class creation
print("\n2. Testing VS Mac Tools class creation...")
try:
    from vs_mac_tools_v2 import InternetCheckerApp
    print("   ✅ InternetCheckerApp imported")
    
    print("   🔧 Creating InternetCheckerApp instance...")
    app = InternetCheckerApp()
    print("   ✅ InternetCheckerApp created")
    
    print(f"   📱 Window title: {app.title()}")
    print(f"   📐 Window geometry: {app.geometry()}")
    
    # Check if window has children
    children = app.winfo_children()
    print(f"   👶 Number of child widgets: {len(children)}")
    
    if len(children) > 0:
        print("   ✅ Window has child widgets")
        for i, child in enumerate(children[:5]):  # Show first 5
            print(f"      Child {i}: {type(child).__name__}")
    else:
        print("   ❌ Window has NO child widgets - THIS IS THE PROBLEM!")
    
    # Force update and check visibility
    app.update()
    print("   ✅ Window updated")
    
    # Check if window is mapped (visible)
    try:
        mapped = app.winfo_viewable()
        print(f"   👁️  Window viewable: {mapped}")
    except:
        print("   ⚠️  Could not check window visibility")
    
    app.destroy()
    print("   ✅ App destroyed")
    
except Exception as e:
    print(f"   ❌ VS Mac Tools test failed: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Check if it's a grid/pack issue
print("\n3. Testing grid layout...")
try:
    app = ctk.CTk()
    app.title("Test 3: Grid Layout")
    app.geometry("400x300")
    
    # Test grid
    frame = ctk.CTkFrame(app)
    frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
    
    label = ctk.CTkLabel(frame, text="Grid Test")
    label.grid(row=0, column=0, padx=10, pady=10)
    
    app.grid_columnconfigure(0, weight=1)
    app.grid_rowconfigure(0, weight=1)
    
    app.update()
    print("   ✅ Grid layout test passed")
    
    app.destroy()
    
except Exception as e:
    print(f"   ❌ Grid test failed: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 DEBUG COMPLETE")
print("If Test 1 passes but Test 2 fails, the issue is in the VS Mac Tools code.")
print("If all tests fail, it's a CustomTkinter/environment issue.")
