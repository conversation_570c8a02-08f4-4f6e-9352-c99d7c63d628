#!/usr/bin/env python3
"""
Simple build script for VS Mac Tools v2 - Creates macOS .app bundle using PyInstaller
"""

import os
import sys
import subprocess
import shutil

def install_tkinter():
    """Install tkinter using system Python"""
    print("🔧 Installing tkinter support...")
    try:
        # Check if tkinter is available
        import tkinter
        print("✅ tkinter is already available")
        return True
    except ImportError:
        print("❌ tkinter not available in current Python")
        print("💡 You may need to install Python with tkinter support")
        print("   Try: brew install python-tk")
        return False

def clean_build():
    """Clean previous build artifacts"""
    dirs_to_clean = ['build', 'dist']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 Cleaned {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"🧹 Cleaned {file}")

def build_simple_app():
    """Build the macOS app using PyInstaller with minimal options"""
    
    print("🚀 Building VS Mac Tools v2 with minimal PyInstaller options...")
    
    # Clean previous builds
    clean_build()
    
    # Check tkinter
    if not install_tkinter():
        print("⚠️  Continuing without tkinter - app may not work properly")
    
    # Simple PyInstaller command
    cmd = [
        'pyinstaller',
        '--onedir',
        '--windowed',
        '--name=VS Mac Tools v2',
        '--clean',
        '--noconfirm',
        
        # Add essential data files
        '--add-data=img:img',
        '--add-data=xml:xml',
        '--add-data=password_dialog.py:.',
        
        # Only essential hidden imports
        '--hidden-import=customtkinter',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=keyring',
        
        # Collect customtkinter data
        '--collect-data=customtkinter',
        
        # macOS bundle identifier
        '--osx-bundle-identifier=com.vonzki.vsmactools',
        
        # Main script
        'vs_mac_tools_v2.py'
    ]
    
    print("🔨 Running PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, text=True)
        print("✅ PyInstaller completed successfully")
        
        # Check if app was created
        app_path = "dist/VS Mac Tools v2.app"
        if os.path.exists(app_path):
            print(f"✅ App created successfully: {app_path}")
            
            # Set executable permissions
            executable_path = f"{app_path}/Contents/MacOS/VS Mac Tools v2"
            if os.path.exists(executable_path):
                os.chmod(executable_path, 0o755)
                print("✅ Set executable permissions")
            
            return True
        else:
            print("❌ App was not created")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller failed: {e}")
        return False

def test_app():
    """Test the built app"""
    app_path = "dist/VS Mac Tools v2.app"
    if os.path.exists(app_path):
        print("🧪 Testing app...")
        try:
            subprocess.run(['open', app_path], check=True)
            print("✅ App launched successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to launch app: {e}")
            return False
    else:
        print("❌ App not found for testing")
        return False

def main():
    """Main build process"""
    print("=" * 60)
    print("VS Mac Tools v2 - Simple macOS App Builder")
    print("=" * 60)
    
    # Check if we're on macOS
    if sys.platform != 'darwin':
        print("❌ This script must be run on macOS")
        sys.exit(1)
    
    # Check if main script exists
    if not os.path.exists('vs_mac_tools_v2.py'):
        print("❌ vs_mac_tools_v2.py not found")
        sys.exit(1)
    
    # Build the app
    if build_simple_app():
        print("\n" + "=" * 60)
        print("✅ BUILD SUCCESSFUL!")
        print("=" * 60)
        print(f"📱 App location: dist/VS Mac Tools v2.app")
        print("🚀 You can now test and distribute this app")
        
        # Ask if user wants to test
        response = input("\n🧪 Do you want to test the app now? (y/n): ")
        if response.lower() in ['y', 'yes']:
            test_app()
    else:
        print("\n" + "=" * 60)
        print("❌ BUILD FAILED!")
        print("=" * 60)
        print("\n💡 Try these solutions:")
        print("1. Install tkinter: brew install python-tk")
        print("2. Use system Python instead of virtual environment")
        print("3. Check that all dependencies are installed")
        sys.exit(1)

if __name__ == "__main__":
    main()
