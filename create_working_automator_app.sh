#!/bin/bash

# Create a working Automator app using command line
# This creates a proper .app bundle that actually works

APP_NAME="VS Mac Tools v2 Working"
APP_DIR="${APP_NAME}.app"
CONTENTS_DIR="${APP_DIR}/Contents"
MACOS_DIR="${CONTENTS_DIR}/MacOS"
RESOURCES_DIR="${CONTENTS_DIR}/Resources"

echo "🚀 Creating working Automator-style app..."

# Clean up existing app
if [ -d "$APP_DIR" ]; then
    rm -rf "$APP_DIR"
fi

# Create app bundle structure
mkdir -p "$MACOS_DIR"
mkdir -p "$RESOURCES_DIR"

echo "📁 Created app bundle structure"

# Create the executable script
cat > "$MACOS_DIR/$APP_NAME" << 'EOF'
#!/bin/bash

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
PYTHON_SCRIPT_DIR="/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker"

# Set environment
export TK_SILENCE_DEPRECATION=1

# Change to the Python script directory
cd "$PYTHON_SCRIPT_DIR"

# Check if Python script exists
if [ ! -f "vs_mac_tools_v2.py" ]; then
    osascript -e 'display alert "Error" message "VS Mac Tools v2 script not found at: '"$PYTHON_SCRIPT_DIR"'"'
    exit 1
fi

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    osascript -e 'display alert "Python Required" message "Python 3 is required but not found. Please install Python 3."'
    exit 1
fi

# Check if required modules are available
python3 -c "import customtkinter, PIL, keyring" 2>/dev/null
if [ $? -ne 0 ]; then
    # Show dialog asking to install dependencies
    result=$(osascript -e 'display dialog "Required Python packages are missing. Install them now?" buttons {"Cancel", "Install"} default button "Install"' 2>/dev/null)
    
    if [[ $result == *"Install"* ]]; then
        # Try to install packages
        osascript -e 'display notification "Installing Python packages..." with title "VS Mac Tools v2"'
        
        # Install packages
        python3 -m pip install --user customtkinter pillow keyring 2>/dev/null
        
        if [ $? -eq 0 ]; then
            osascript -e 'display notification "Packages installed successfully!" with title "VS Mac Tools v2"'
        else
            osascript -e 'display alert "Installation Failed" message "Could not install required packages. Please install manually: pip3 install customtkinter pillow keyring"'
            exit 1
        fi
    else
        exit 1
    fi
fi

# Launch the Python app
echo "Launching VS Mac Tools v2..."
python3 vs_mac_tools_v2.py

# Check if it exited with an error (but ignore normal exit and KeyboardInterrupt)
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ] && [ $EXIT_CODE -ne 130 ] && [ $EXIT_CODE -ne 2 ]; then
    osascript -e 'display alert "Application Error" message "VS Mac Tools v2 exited with an error. Check the terminal for details."'
fi
EOF

# Make the script executable
chmod +x "$MACOS_DIR/$APP_NAME"

echo "🚀 Created executable script"

# Create Info.plist
cat > "$CONTENTS_DIR/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>$APP_NAME</string>
    <key>CFBundleIdentifier</key>
    <string>com.vonzki.vsmactools</string>
    <key>CFBundleName</key>
    <string>VS Mac Tools v2</string>
    <key>CFBundleDisplayName</key>
    <string>VS Mac Tools v2</string>
    <key>CFBundleVersion</key>
    <string>2.0.1</string>
    <key>CFBundleShortVersionString</key>
    <string>2.0.1</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleIconFile</key>
    <string>vsmactool.icns</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSMinimumSystemVersion</key>
    <string>10.15.0</string>
    <key>NSAppleScriptEnabled</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
</dict>
</plist>
EOF

echo "📄 Created Info.plist"

# Copy icon if it exists
if [ -f "img/vsmactool.icns" ]; then
    cp "img/vsmactool.icns" "$RESOURCES_DIR/"
    echo "🎨 Copied app icon"
else
    echo "⚠️  Icon not found, app will use default icon"
fi

echo "✅ Created working app: $APP_DIR"
echo ""
echo "🎯 To test the app:"
echo "   open '$APP_DIR'"
echo ""
echo "📦 App size: $(du -sh "$APP_DIR" | cut -f1)"

# Test if the app can be opened
echo ""
echo "🧪 Testing app launch..."
open "$APP_DIR"

echo ""
echo "✅ App should now be launching!"
echo "   If it works, you have a fully functional standalone app."
echo ""
echo "📋 What this app does:"
echo "   ✅ Checks for Python 3"
echo "   ✅ Checks for required packages"
echo "   ✅ Offers to install missing packages"
echo "   ✅ Launches your VS Mac Tools v2 with full UI"
echo "   ✅ Shows your custom icon"
echo "   ✅ Handles errors gracefully"
