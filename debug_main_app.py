#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

import customtkinter as ctk

class SimpleApp(ctk.CTk):
    def __init__(self):
        print("🔧 Initializing SimpleApp...")
        
        try:
            # Try the problematic DPI call
            print("   Testing DPI deactivation...")
            ctk.deactivate_automatic_dpi_awareness()
            print("   ✅ DPI deactivation successful")
        except Exception as e:
            print(f"   ⚠️ DPI deactivation failed: {e}")
        
        try:
            print("   Calling super().__init__()...")
            super().__init__()
            print("   ✅ Super init successful")
        except Exception as e:
            print(f"   ❌ Super init failed: {e}")
            return
        
        print("   Setting up window...")
        self.title("Simple Test App")
        self.geometry("800x600")
        
        print("   Setting appearance mode...")
        ctk.set_appearance_mode("dark")
        
        print("   Creating UI elements...")
        
        # Simple label
        self.label = ctk.CTkLabel(self, text="Simple Test App", font=("Arial", 20))
        self.label.pack(pady=20)
        
        # Simple button
        self.button = ctk.CTkButton(self, text="Test Button", command=self.button_click)
        self.button.pack(pady=10)
        
        print("   ✅ UI elements created")
        
        # Force update
        try:
            print("   Updating window...")
            self.update()
            print("   ✅ Window updated")
        except Exception as e:
            print(f"   ❌ Window update failed: {e}")
    
    def button_click(self):
        print("Button clicked!")

def main():
    print("🚀 Starting Simple App Test...")
    
    try:
        app = SimpleApp()
        print("✅ App created successfully")
        print("🎯 Starting mainloop...")
        app.mainloop()
        print("✅ App closed")
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
