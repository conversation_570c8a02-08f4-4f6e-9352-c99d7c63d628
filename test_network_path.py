#!/usr/bin/env python3
"""
Test script to verify network path functionality
"""

import os
import subprocess
import time

def test_network_path():
    """Test the network path mounting functionality"""
    
    # Network configuration
    network_path = "/Volumes/share"
    server_address = "**************"
    share_name = "share"
    smb_url = f"smb://{server_address}/{share_name}"
    
    print("Testing network path functionality...")
    print(f"Network path: {network_path}")
    print(f"SMB URL: {smb_url}")
    print()
    
    # Check if the mount point exists
    if os.path.exists(network_path):
        print(f"✓ Mount point {network_path} exists")
        
        # Try to list files to see if it's actually mounted
        try:
            files = os.listdir(network_path)
            print(f"✓ Mount point is accessible, found {len(files)} items")
            if files:
                print(f"  Sample files: {files[:5]}")  # Show first 5 files
        except PermissionError:
            print("✗ Mount point exists but permission denied (likely not mounted)")
        except Exception as e:
            print(f"✗ Error accessing mount point: {str(e)}")
    else:
        print(f"✗ Mount point {network_path} does not exist")
    
    print()
    
    # Test mounting using AppleScript
    print("Testing AppleScript mount method...")
    try:
        applescript = f'''
        tell application "Finder"
            try
                mount volume "{smb_url}"
                return true
            on error
                return false
            end try
        end tell
        '''
        result = subprocess.run(['osascript', '-e', applescript],
                              capture_output=True, text=True, timeout=15)
        
        if "true" in result.stdout.lower():
            print("✓ AppleScript mount method succeeded")
        else:
            print(f"✗ AppleScript mount method failed: {result.stdout}")
    except Exception as e:
        print(f"✗ AppleScript mount method error: {str(e)}")
    
    print()
    
    # Check again after mount attempt
    if os.path.exists(network_path):
        try:
            files = os.listdir(network_path)
            print(f"✓ After mount attempt: Mount point is accessible, found {len(files)} items")
            return True
        except Exception as e:
            print(f"✗ After mount attempt: Error accessing mount point: {str(e)}")
            return False
    else:
        print(f"✗ After mount attempt: Mount point {network_path} still does not exist")
        return False

def test_file_dialog_path():
    """Test if the file dialog would use the correct default path"""
    import tkinter as tk
    from tkinter import filedialog
    
    print("\nTesting file dialog default path...")
    
    # Network configuration
    network_path = "/Volumes/share"
    
    # Check what path would be used
    if os.path.exists(network_path) and os.path.isdir(network_path):
        try:
            # Verify we can actually access the directory
            os.listdir(network_path)
            initialdir = network_path
            print(f"✓ File dialog would use network path: {initialdir}")
        except (PermissionError, OSError) as e:
            # If we can't access it, fall back to desktop
            initialdir = os.path.expanduser("~/Desktop")
            print(f"✗ Network path not accessible, would use desktop: {initialdir}")
    else:
        # Fall back to desktop if mounting failed
        initialdir = os.path.expanduser("~/Desktop")
        print(f"✗ Network path not available, would use desktop: {initialdir}")
    
    return initialdir

if __name__ == "__main__":
    print("VS Mac Tools Network Path Test")
    print("=" * 40)
    
    # Test network mounting
    mount_success = test_network_path()
    
    # Test file dialog path
    default_path = test_file_dialog_path()
    
    print("\nSummary:")
    print(f"Network mount successful: {'Yes' if mount_success else 'No'}")
    print(f"Default file dialog path: {default_path}")
    
    if mount_success:
        print("\n✓ Network functionality should work correctly in the app")
    else:
        print("\n⚠ Network functionality may fall back to desktop in the app")
