#!/usr/bin/env python3
"""
Debug script to identify UI initialization issues
"""

import sys
import traceback
import os

def debug_ui():
    try:
        print("=== UI Debug Session ===")
        print(f"Python version: {sys.version}")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Python path: {sys.path}")
        
        # Test imports step by step
        print("\n=== Testing Imports ===")
        
        try:
            import tkinter as tk
            print("✅ tkinter imported successfully")
            
            # Test basic tkinter
            root = tk.Tk()
            root.title("tkinter test")
            root.geometry("200x100")
            label = tk.Label(root, text="tkinter works!")
            label.pack()
            print("✅ Basic tkinter window created")
            root.destroy()
            
        except Exception as e:
            print(f"❌ tkinter error: {e}")
            traceback.print_exc()
            
        try:
            import customtkinter as ctk
            print("✅ customtkinter imported successfully")
            
            # Test basic customtkinter
            ctk.set_appearance_mode("dark")
            app = ctk.CTk()
            app.title("CustomTkinter Test")
            app.geometry("300x200")
            
            label = ctk.CTkLabel(app, text="CustomTkinter works!")
            label.pack(pady=20)
            
            button = ctk.CTkButton(app, text="Test Button")
            button.pack(pady=10)
            
            print("✅ Basic CustomTkinter window created")
            
            # Test if window shows
            app.update()
            print("✅ CustomTkinter window updated")
            
            app.destroy()
            print("✅ CustomTkinter window destroyed")
            
        except Exception as e:
            print(f"❌ customtkinter error: {e}")
            traceback.print_exc()
            
        try:
            from PIL import Image
            print("✅ PIL imported successfully")
        except Exception as e:
            print(f"❌ PIL error: {e}")
            
        try:
            import keyring
            print("✅ keyring imported successfully")
        except Exception as e:
            print(f"❌ keyring error: {e}")
            
        # Test main app import
        print("\n=== Testing Main App Import ===")
        try:
            import vs_mac_tools_v2
            print("✅ vs_mac_tools_v2 imported successfully")
            
            # Test app creation
            print("Creating main app instance...")
            app = vs_mac_tools_v2.InternetCheckerApp()
            print("✅ Main app instance created")
            
            # Test if app has UI elements
            if hasattr(app, 'process_frame'):
                print("✅ process_frame exists")
            else:
                print("❌ process_frame missing")
                
            if hasattr(app, 'hardware_frame'):
                print("✅ hardware_frame exists")
            else:
                print("❌ hardware_frame missing")
                
            if hasattr(app, 'shortcuts_frame'):
                print("✅ shortcuts_frame exists")
            else:
                print("❌ shortcuts_frame missing")
                
            # Test window properties
            try:
                app.update()
                print(f"✅ App geometry: {app.geometry()}")
                print(f"✅ App title: {app.title()}")
            except Exception as e:
                print(f"❌ App update error: {e}")
            
            print("✅ App created successfully - starting mainloop for 3 seconds...")
            app.after(3000, app.quit)  # Auto-quit after 3 seconds
            app.mainloop()
            
        except Exception as e:
            print(f"❌ Main app error: {e}")
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Debug session error: {e}")
        traceback.print_exc()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    debug_ui()
