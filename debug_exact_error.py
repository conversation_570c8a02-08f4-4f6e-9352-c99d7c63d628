#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

import sys
import traceback

print("🔍 EXACT ERROR DEBUG for VS Mac Tools v2")
print("=" * 50)

try:
    print("1. Testing imports...")
    import customtkinter as ctk
    print("   ✅ CustomTkinter imported")
    
    from PIL import Image
    print("   ✅ PIL imported")
    
    import keyring
    print("   ✅ Keyring imported")
    
    print("\n2. Testing basic CTk creation...")
    app = ctk.CTk()
    print("   ✅ CTk() created")
    
    app.title("Debug Test")
    print("   ✅ Title set")
    
    app.geometry("400x300")
    print("   ✅ Geometry set")
    
    print("\n3. Testing widget creation...")
    label = ctk.CTkLabel(app, text="Test Label")
    print("   ✅ Label created")
    
    label.pack(pady=20)
    print("   ✅ Label packed")
    
    print("\n4. Testing window update...")
    app.update()
    print("   ✅ Window updated")
    
    print("\n5. Testing VS Mac Tools import...")
    from vs_mac_tools_v2 import InternetCheckerApp
    print("   ✅ InternetCheckerApp imported")
    
    print("\n6. Creating VS Mac Tools instance...")
    vs_app = InternetCheckerApp()
    print("   ✅ InternetCheckerApp created")
    
    print(f"   📱 Title: {vs_app.title()}")
    print(f"   📐 Geometry: {vs_app.geometry()}")
    
    # Check children
    children = vs_app.winfo_children()
    print(f"   👶 Children count: {len(children)}")
    
    if len(children) == 0:
        print("   ❌ NO CHILDREN - THIS IS THE BLANK UI PROBLEM!")
    else:
        print("   ✅ Has children widgets")
        for i, child in enumerate(children[:3]):
            print(f"      Child {i}: {type(child).__name__}")
    
    print("\n7. Testing window visibility...")
    vs_app.update()
    print("   ✅ VS App updated")
    
    # Clean up
    app.destroy()
    vs_app.destroy()
    
    print("\n✅ ALL TESTS PASSED - UI should be working!")
    
except Exception as e:
    print(f"\n❌ ERROR FOUND: {e}")
    print("\n📋 FULL TRACEBACK:")
    traceback.print_exc()
    
    print(f"\n🔍 ERROR TYPE: {type(e).__name__}")
    print(f"🔍 ERROR MESSAGE: {str(e)}")
    
    # Try to get more details
    if hasattr(e, '__cause__') and e.__cause__:
        print(f"🔍 CAUSED BY: {e.__cause__}")
    
    print("\n💡 This is the exact issue causing the blank UI!")

print("\n" + "=" * 50)
print("🎯 DEBUG COMPLETE")
