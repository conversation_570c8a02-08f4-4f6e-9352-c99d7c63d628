#!/bin/bash

# Script to build the VS Mac Tools v2 application with PyInstaller using system Python
# This script uses system Python to avoid tkinter issues

echo "Building VS Mac Tools v2 with PyInstaller using system Python..."

# Function to display usage information
show_usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -c, --clean   Clean up previous builds before building"
    echo "  -h, --help    Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --clean    # Clean and build"
}

# Default values
CLEAN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if system Python has the required packages
echo "Checking system Python packages..."

# Install required packages for system Python
echo "Installing required packages for system Python..."
python3 -m pip install --user customtkinter pillow pyinstaller keyring darkdetect --break-system-packages

# Clean up previous builds if requested
if [ "$CLEAN" = true ]; then
    echo "Cleaning up previous builds..."
    rm -rf build dist
    find . -name "*.pyc" -delete
    find . -name "__pycache__" -delete
fi

# Verify required files exist
echo "Checking required files..."

if [ ! -f "vs_mac_tools_v2.py" ]; then
    echo "Error: vs_mac_tools_v2.py not found!"
    exit 1
fi

if [ ! -f "password_dialog.py" ]; then
    echo "Error: password_dialog.py not found!"
    exit 1
fi

if [ ! -d "img" ]; then
    echo "Error: img directory not found!"
    exit 1
fi

# Check for icon files
ICON_FOUND=false
if [ -f "img/vs_mac_tool_v2.icns" ]; then
    ICON_FOUND=true
    echo "Using icon: img/vs_mac_tool_v2.icns"
elif [ -f "img/vs_icns/checkADE2.icns" ]; then
    ICON_FOUND=true
    echo "Using icon: img/vs_icns/checkADE2.icns"
elif [ -f "img/bluetooth.icns" ]; then
    ICON_FOUND=true
    echo "Using icon: img/bluetooth.icns"
else
    echo "Warning: No suitable icon found. App will use default icon."
fi

# Build the application using the spec file with system Python
echo "Building application with PyInstaller using system Python..."
python3 -m PyInstaller vs_mac_tools_v2.spec --clean --noconfirm

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Build successful!"
    
    # Check if the app was created
    if [ -d "dist/VS Mac Tools.app" ]; then
        echo "Application created at: dist/VS Mac Tools.app"
        
        # Get app size
        APP_SIZE=$(du -sh "dist/VS Mac Tools.app" | cut -f1)
        echo "Application size: $APP_SIZE"
        
        # Make the app executable
        chmod +x "dist/VS Mac Tools.app/Contents/MacOS/VS Mac Tools"
        
        echo ""
        echo "Build completed successfully!"
        echo "You can now run the application from: dist/VS Mac Tools.app"
        echo "Or copy it to your Applications folder."
        
        # Ask if user wants to open the app
        read -p "Do you want to open the application now? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "Opening the application..."
            open "dist/VS Mac Tools.app"
        fi
    else
        echo "Error: Application bundle was not created!"
        exit 1
    fi
else
    echo "Build failed. See error messages above."
    exit 1
fi
