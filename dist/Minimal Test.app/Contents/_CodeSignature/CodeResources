<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/base_library.zip</key>
		<data>
		1E5rM+H73s/OvArd4jKwgB4mUpk=
		</data>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/icon-windowed.icns</key>
		<data>
		eEHOuYpZLB0vKGVIWGZOh5rH8+o=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oquNTqBnHpMlz5394zrC17FGi80=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2ab8d4ea0671e9325cf9dfde33ac2d7b1468bcd"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x7R3O7pZUNBEfQAdsl54ix4LaJ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7b4773bba5950d0447d001db25e788b1e0b689e"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			vOar1FXZ7WcuWRqbNA5cYJVqmWw=
			</data>
			<key>requirement</key>
			<string>cdhash H"bce6abd455d9ed672e591a9b340e5c60956a996c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			IEdMZTK9mMXiCDCIQU45IH9mD9E=
			</data>
			<key>requirement</key>
			<string>cdhash H"20474c6532bd98c5e2083088414e39207f660fd1"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8aTGLWPNL8Z0mq/ZCCqo4ITzkTw=
			</data>
			<key>requirement</key>
			<string>cdhash H"f1a4c62d63cd2fc6749aafd9082aa8e084f3913c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			a4upi7rj26KgmwWmOVWseAwGv+8=
			</data>
			<key>requirement</key>
			<string>cdhash H"6b8ba98bbae3dba2a09b05a63955ac780c06bfef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x9bP0OkLtFZ19oat7KODBojVte8=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7d6cfd0e90bb45675f686adeca3830688d5b5ef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			2JRI3oDk/hiPO6KHLr1W3RGSs9g=
			</data>
			<key>requirement</key>
			<string>cdhash H"d89448de80e4fe188f3ba2872ebd56dd1192b3d8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			S9OWwquTmkEKpIFW/jqzt1HHtug=
			</data>
			<key>requirement</key>
			<string>cdhash H"4bd396c2ab939a410aa48156fe3ab3b751c7b6e8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jeRVXzKhN1QeR2XcCm4BMJPTLUE=
			</data>
			<key>requirement</key>
			<string>cdhash H"8de4555f32a137541e4765dc0a6e013093d32d41"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oFtR66WJm5ejzYocVozZR9+7P+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"a05b51eba5899b97a3cd8a1c568cd947dfbb3fe0"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			CA17m/ktNfM9WuMFAkoquWHpDmA=
			</data>
			<key>requirement</key>
			<string>cdhash H"080d7b9bf92d35f33d5ae305024a2ab961e90e60"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1NCCXcJJRfMo8FBrOsPdJwbRoWQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4d0825dc24945f328f0506b3ac3dd2706d1a164"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DA9qe8wlu/qHWZWmnl6S9M/Yndk=
			</data>
			<key>requirement</key>
			<string>cdhash H"0c0f6a7bcc25bbfa875995a69e5e92f4cfd89dd9"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ud90uzcjWeDAVJwDgJ6hgN65Sy0=
			</data>
			<key>requirement</key>
			<string>cdhash H"51df74bb372359e0c0549c03809ea180deb94b2d"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LsusOeFUh1notacct2CcfQQPznM=
			</data>
			<key>requirement</key>
			<string>cdhash H"2ecbac39e1548759e8b5a71cb7609c7d040fce73"</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Aphb79tmqTxGnxAxWwWYRMZHhJo=
			</data>
			<key>requirement</key>
			<string>cdhash H"02985befdb66a93c469f10315b059844c647849a"</string>
		</dict>
		<key>Frameworks/Python3</key>
		<dict>
			<key>symlink</key>
			<string>Python3.framework/Versions/3.9/Python3</string>
		</dict>
		<key>Frameworks/Python3.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			xfccs665Mvtbx0rTtO+URkqYpiw=
			</data>
			<key>requirement</key>
			<string>cdhash H"c5f71cb3aeb932fb5bc74ad3b4ef94464a98a62c"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/customtkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			tOb7dQTf6C4SgXjFrdii0YdoH98=
			</data>
			<key>requirement</key>
			<string>cdhash H"b4e6fb7504dfe82e128178c5add8a2d187681fdf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oLFHeppZt74mUcf28+znvBaZhS4=
			</data>
			<key>requirement</key>
			<string>cdhash H"a0b1477a9a59b7be2651c7f6f3ece7bc1699852e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			2ZggNr90cMKTpZdKIohz0LAbIxA=
			</data>
			<key>requirement</key>
			<string>cdhash H"d9982036bf7470c293a5974a228873d0b01b2310"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gxIMJtc5FrnDgVYyWqGa+qrXCvI=
			</data>
			<key>requirement</key>
			<string>cdhash H"83120c26d73916b9c38156325aa19afaaad70af2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			4I0qigfTbxMMoryy/320d1DJWrA=
			</data>
			<key>requirement</key>
			<string>cdhash H"e08d2a8a07d36f130ca2bcb2ff7db47750c95ab0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			w8bd0HY48RR2pxJlumKpBiSml0o=
			</data>
			<key>requirement</key>
			<string>cdhash H"c3c6ddd07638f11476a71265ba62a90624a6974a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Tjk+5/cgXDCQHbNcte8FbhJjUew=
			</data>
			<key>requirement</key>
			<string>cdhash H"4e393ee7f7205c30901db35cb5ef056e126351ec"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lnS/WH/5FnvkQ/L74dFZRh+77/4=
			</data>
			<key>requirement</key>
			<string>cdhash H"9674bf587ff9167be443f2fbe1d159461fbbeffe"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gpQCVNJ0ugJD92hClU2/M8myqL4=
			</data>
			<key>requirement</key>
			<string>cdhash H"82940254d274ba0243f76842954dbf33c9b2a8be"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rWdLsbDRMVOasWha4i95JZbRNYA=
			</data>
			<key>requirement</key>
			<string>cdhash H"ad674bb1b0d131539ab1685ae22f792596d13580"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			g/Wwb7Mhden6vEvWFnWzyQdMzP0=
			</data>
			<key>requirement</key>
			<string>cdhash H"83f5b06fb32175e9fabc4bd61675b3c9074cccfd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LstjFvzqiQheAB6bjOT4NfkwO7E=
			</data>
			<key>requirement</key>
			<string>cdhash H"2ecb6316fcea89085e001e9b8ce4f835f9303bb1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hdj9PgdnGmN111BEIgrdgIzWasQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"85d8fd3e07671a6375d75044220add808cd66ac4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9b2VjtMsepXEZzK37qBtVf9Xugc=
			</data>
			<key>requirement</key>
			<string>cdhash H"f5bd958ed32c7a95c46732b7eea06d55ff57ba07"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			z+nnfJso+3S3G4jvB0OfOiIT2lk=
			</data>
			<key>requirement</key>
			<string>cdhash H"cfe9e77c9b28fb74b71b88ef07439f3a2213da59"</string>
		</dict>
		<key>Frameworks/lib-dynload/_elementtree.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dAJxzV/ViKuOfokJ7bEurTG/79Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"740271cd5fd588ab8e7e8909edb12ead31bfefd4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			49dCHqx3E7qcP7AZks68v2j9eJ8=
			</data>
			<key>requirement</key>
			<string>cdhash H"e3d7421eac7713ba9c3fb01992cebcbf68fd789f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			uGeiQ3WFbtWbyzYGRkeBiJXzAME=
			</data>
			<key>requirement</key>
			<string>cdhash H"b867a24375856ed59bcb36064647818895f300c1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gCLFJ2RC1Ybo75EZ5KkXcIdMNdA=
			</data>
			<key>requirement</key>
			<string>cdhash H"8022c5276442d586e8ef9119e4a91770874c35d0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Vko8YBVAUokVbjLhZ1clftuR4Bk=
			</data>
			<key>requirement</key>
			<string>cdhash H"564a3c6015405289156e32e16757257edb91e019"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pVZ5L8i+J4G+NgOySvymWCMSIDc=
			</data>
			<key>requirement</key>
			<string>cdhash H"a556792fc8be2781be3603b24afca65823122037"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			J8YzMjuLivaeTqB2fH9qgGOIV80=
			</data>
			<key>requirement</key>
			<string>cdhash H"27c633323b8b8af69e4ea0767c7f6a80638857cd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			H71fWIo/km7Um9B05wqqP7XtjVs=
			</data>
			<key>requirement</key>
			<string>cdhash H"1fbd5f588a3f926ed49bd074e70aaa3fb5ed8d5b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5II0IusaKTTPg6pqCfsnVwjtDVg=
			</data>
			<key>requirement</key>
			<string>cdhash H"e4823422eb1a2934cf83aa6a09fb275708ed0d58"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			j6e0Oc2A2kGHizBg6LoRpUuv1Vc=
			</data>
			<key>requirement</key>
			<string>cdhash H"8fa7b439cd80da41878b3060e8ba11a54bafd557"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			xKfHOBdR+ukN4ru7r7pKrIDd+2s=
			</data>
			<key>requirement</key>
			<string>cdhash H"c4a7c7381751fae90de2bbbbafba4aac80ddfb6b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			76PVZWJLa9Dzg1RggXL86SVGrv0=
			</data>
			<key>requirement</key>
			<string>cdhash H"efa3d565624b6bd0f38354608172fce92546aefd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JLtqMUr2n7ve382hdVQl3HzvOZ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"24bb6a314af69fbbdedfcda1755425dc7cef399e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			orqIoN0gfBGeTLx7E0UDNFag4L8=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2ba88a0dd207c119e4cbc7b1345033456a0e0bf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Inxn4N86qQ4GSPUvE5y0+2q0Vp4=
			</data>
			<key>requirement</key>
			<string>cdhash H"227c67e0df3aa90e0648f52f139cb4fb6ab4569e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			d7RZJBLPO5BBIdAeL4eRZ66b4p8=
			</data>
			<key>requirement</key>
			<string>cdhash H"77b4592412cf3b904121d01e2f879167ae9be29f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha256.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wWUiVqAobe/qSSSxfKhIxQzbyBA=
			</data>
			<key>requirement</key>
			<string>cdhash H"c1652256a0286defea4924b17ca848c50cdbc810"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oczwGtfnmXLZke5eV6L82jKu9MU=
			</data>
			<key>requirement</key>
			<string>cdhash H"a1ccf01ad7e79972d991ee5e57a2fcda32aef4c5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha512.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			V82aDE5pmrjd84lfYFZkAe9yEDo=
			</data>
			<key>requirement</key>
			<string>cdhash H"57cd9a0c4e699ab8ddf3895f60566401ef72103a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kDcuSqwI0/qdwZx0iqo+7x5UhtU=
			</data>
			<key>requirement</key>
			<string>cdhash H"90372e4aac08d3fa9dc19c748aaa3eef1e5486d5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			AxQt4QLFuFjSwjQQkF6lSuNi/Ds=
			</data>
			<key>requirement</key>
			<string>cdhash H"03142de102c5b858d2c23410905ea54ae362fc3b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OlIekPZHjyV0zoUONXfnj1qfeSc=
			</data>
			<key>requirement</key>
			<string>cdhash H"3a521e90f6478f2574ce850e3577e78f5a9f7927"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1NrYIRSaMlWPADbiLXzOFBDmDbI=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4dad821149a32558f0036e22d7cce1410e60db2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_tkinter.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3KjFCZbcFxDmKahk39ZcpQ3nHSE=
			</data>
			<key>requirement</key>
			<string>cdhash H"dca8c50996dc1710e629a864dfd65ca50de71d21"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GCDMA/vB5UV/Y3PNhNyNX9Y5Blw=
			</data>
			<key>requirement</key>
			<string>cdhash H"1820cc03fbc1e5457f6373cd84dc8d5fd639065c"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rRRGjdOAIGReLPtVnRQcFbDL7Lg=
			</data>
			<key>requirement</key>
			<string>cdhash H"ad14468dd38020645e2cfb559d141c15b0cbecb8"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SnfOQE602PxkXU8Sqsqt/qvG0n8=
			</data>
			<key>requirement</key>
			<string>cdhash H"4a77ce404eb4d8fc645d4f12aacaadfeabc6d27f"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DPffwejekKqrtM58HDLir8rzDmE=
			</data>
			<key>requirement</key>
			<string>cdhash H"0cf7dfc1e8de90aaabb4ce7c1c32e2afcaf30e61"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			utebxTBPq72YVrOZYSp1hlP5zQo=
			</data>
			<key>requirement</key>
			<string>cdhash H"bad79bc5304fabbd9856b399612a758653f9cd0a"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5kjr2ePoZS2d3aF2cJCvQ+JmLqM=
			</data>
			<key>requirement</key>
			<string>cdhash H"e648ebd9e3e8652d9ddda1767090af43e2662ea3"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Kn1KfyMUnTvzUeQUgQ67B1w9qmI=
			</data>
			<key>requirement</key>
			<string>cdhash H"2a7d4a7f23149d3bf351e414810ebb075c3daa62"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LNNPP7H9dr7Y7xVkqL4WFUEqLJc=
			</data>
			<key>requirement</key>
			<string>cdhash H"2cd34f3fb1fd76bed8ef1564a8be1615412a2c97"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			vVAhejDVyYVHVqUE4krkBq5Bv58=
			</data>
			<key>requirement</key>
			<string>cdhash H"bd50217a30d5c9854756a504e24ae406ae41bf9f"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			FVX0CHY5twXsbA4oiAkcHZ9b1rs=
			</data>
			<key>requirement</key>
			<string>cdhash H"1555f4087639b705ec6c0e2888091c1d9f5bd6bb"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			nlLgVBZ+FEWAyz64zo78mbfKy5I=
			</data>
			<key>requirement</key>
			<string>cdhash H"9e52e054167e144580cb3eb8ce8efc99b7cacb92"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ipinocrWP1531ykfVFwUcYsPSVM=
			</data>
			<key>requirement</key>
			<string>cdhash H"8a98a7a1cad63f5e77d7291f545c14718b0f4953"</string>
		</dict>
		<key>Frameworks/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Frameworks/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Frameworks/numpy/_core/_multiarray_tests.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QQqEtMeF5G1tYM/BPink26awOUU=
			</data>
			<key>requirement</key>
			<string>cdhash H"410a84b4c785e46d6d60cfc13e29e4dba6b03945"</string>
		</dict>
		<key>Frameworks/numpy/_core/_multiarray_umath.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dNF9QjOQvQj+1jchiE65yJBcEOQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"74d17d423390bd08fed63721884eb9c8905c10e4"</string>
		</dict>
		<key>Frameworks/numpy/fft/_pocketfft_umath.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			CzJMjZwx19/jGTOEU5q939e+ZYc=
			</data>
			<key>requirement</key>
			<string>cdhash H"0b324c8d9c31d7dfe3193384539abddfd7be6587"</string>
		</dict>
		<key>Frameworks/numpy/linalg/_umath_linalg.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hpVOBrRICw5M1+D1YNifHF1wUu0=
			</data>
			<key>requirement</key>
			<string>cdhash H"86954e06b4480b0e4cd7e0f560d89f1c5d7052ed"</string>
		</dict>
		<key>Frameworks/numpy/random/_bounded_integers.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UkeHaDphtY48UVzZWIngCKHxv2E=
			</data>
			<key>requirement</key>
			<string>cdhash H"524787683a61b58e3c515cd95889e008a1f1bf61"</string>
		</dict>
		<key>Frameworks/numpy/random/_common.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gPVCKjXsS8VlVcKBUi1QgiY9PBE=
			</data>
			<key>requirement</key>
			<string>cdhash H"80f5422a35ec4bc56555c281522d5082263d3c11"</string>
		</dict>
		<key>Frameworks/numpy/random/_generator.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oZaMChGh9hlgFyZG6oJV5tOdRrw=
			</data>
			<key>requirement</key>
			<string>cdhash H"a1968c0a11a1f61960172646ea8255e6d39d46bc"</string>
		</dict>
		<key>Frameworks/numpy/random/_mt19937.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6bb1vVfEpjujG6ixv/CHvXeNgC8=
			</data>
			<key>requirement</key>
			<string>cdhash H"e9b6f5bd57c4a63ba31ba8b1bff087bd778d802f"</string>
		</dict>
		<key>Frameworks/numpy/random/_pcg64.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			TuWStm/4Umscq2jm3UgVN233IVE=
			</data>
			<key>requirement</key>
			<string>cdhash H"4ee592b66ff8526b1cab68e6dd4815376df72151"</string>
		</dict>
		<key>Frameworks/numpy/random/_philox.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MZfapYpo0sGpe/X+6VLlb/4bvsQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"3197daa58a68d2c1a97bf5fee952e56ffe1bbec4"</string>
		</dict>
		<key>Frameworks/numpy/random/_sfc64.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OD6xFll63VvaNWLiyO7E1/CwPOc=
			</data>
			<key>requirement</key>
			<string>cdhash H"383eb116597add5bda3562e2c8eec4d7f0b03ce7"</string>
		</dict>
		<key>Frameworks/numpy/random/bit_generator.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KA6Prpqqfi3HvpdzcXVpwMbBFdw=
			</data>
			<key>requirement</key>
			<string>cdhash H"280e8fae9aaa7e2dc7be9773717569c0c6c115dc"</string>
		</dict>
		<key>Frameworks/numpy/random/mtrand.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			65pgsJvIPMbRg1TJyia33k+FiVg=
			</data>
			<key>requirement</key>
			<string>cdhash H"eb9a60b09bc83cc6d18354c9ca26b7de4f858958"</string>
		</dict>
		<key>Resources/PIL</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/PIL</string>
		</dict>
		<key>Resources/Python3</key>
		<dict>
			<key>symlink</key>
			<string>Python3.framework/Versions/3.9/Python3</string>
		</dict>
		<key>Resources/Python3.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python3.framework</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			tFoAAHZPi1ZzWKx74xaQIOP7ldJOOMXF3SfrEWU0Vu0=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/icon-windowed.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			uQo7VuWRab4Phv4EEGmfQsyqFqDIXZgO8OtgaAMvCzY=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Resources/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Resources/numpy</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/numpy</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
