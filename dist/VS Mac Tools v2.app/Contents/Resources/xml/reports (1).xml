<?xml version="1.0" encoding="UTF-8"?><root><report>
    <blancco_data>
        <description>
            <document_id>da7fb9b5-f6bc-4dbd-93db-f944d6d6b663</document_id>
            <document_log>
                <log_entry>
                    <author>
                        <product_name id="30" name="Blancco Eraser for Apple Devices">Blancco 5 Mobile</product_name>
                        <product_version>BEAD_5.7</product_version>
                        <product_revision>BEAD_5.7</product_revision>
                    </author>
                    <date>2025-05-13T15:04:27+0900</date>
                    <integrity>exYdcPdTfBrAtosBmMkHMc0DzznGvumcx4WHjFCmOlH3RepKMa9KBw2rkdhaj2HllGEgRdB4dueEO6t4VcImprD1g7LtzgxdI99fMd+QUJ8DopZnV3BiIgMlcrNhKtJw1/BwlcjicJril8kficfGcx6NGTYvrCujnAxWStMov1/jlT6uP8LI43rk1Ue/RGTTDdgyRhWhmKts1EnTNB+K5/3rlIUD006q4eb6dc00Fp7AGTdFJO5sNYsLwgMz6VKPjjdrQ0E0UZ4+S4zf1w2TYAGGoLIUr7Iwea0O+DPtAwggcKdZvWGMBO2/sOXgF0fGC7R9qdd/LUaOQEqzpZ66Qg==</integrity>
                    <key identifier="300"/>
                </log_entry>
                <log_entry>
                    <author>
                        <product_name id="51" name="Blancco Management Console">Blancco Management Console</product_name>
                        <product_version>5.20.0</product_version>
                        <product_revision>302ee2bad746</product_revision>
                    </author>
                    <date>2025-05-13T15:04:27+0900</date>
                    <integrity>VFjn/oM+a5zBiMWK4634zV2l7Cvxat2lZUPj/TPoj8qPanmgd+KepENhooEMhsI7ZH6hP4rK4swod99Wq2Iyr7O6jiTlCGg0E5QdrkMJvGuKZIo9yjXHcHbQNG9y2uP/EBBF6r2tiV9YQ3uDNpm84PtDNu8Vbzjank0ZFUDH9QbtNG07iVL53g/v1sQTEqc6/HhFRQk5DZ8cW3ds40fRGMEnI1S7RZc/rZcT4lvto0uUmBIQuaOllShxYT1xMwTHssVzWEkmUjT3PXucW3PlqGDGC7NjGuEnOOP7njhDYdVosZuUk0VVnlL6Mw+HF2nmwKrTgKA6MTUYosA2Y+GS4g==</integrity>
                    <key identifier="100"/>
                </log_entry>
                <log_entry>
                    <author>
                        <product_name id="51" name="Blancco Management Console">Blancco Management Console</product_name>
                        <product_version>5.20.0</product_version>
                        <product_revision>302ee2bad746</product_revision>
                    </author>
                    <date>2025-05-13T17:19:10+0900</date>
                    <integrity>kpJHoPiU5EzP7lgmgLTcIiuHhM9EGazBJR7EfyPKibaoRcy2OOLpLWuzmj3aq0Wn1EJJ5nV/1L03CJiVKQ6xSC8C+hNMn0CkFEGzTRNNfv/XquKFO4kbIAdneSI8+LTjHrgjdHVX7dOyxdJ3vycE3QsISyqZafM445kVgdDgMnAxYTIe6zFqyL+Yaqmhib64eQ02R7An+4mivdWQfsjDCW0nmpSylPNG+pryRYQRcqcpViwz1Qk0RJNaZewIhfIWikwsT1N1m6CbcpUrLnFbxoete9c8IyHW3OBa4K6eKoe8X7OsguBsohFptya0IqwGLl62UBvy4fv6a7ZTsexwrg==</integrity>
                    <key identifier="100"/>
                </log_entry>
                <log_entry>
                    <author>
                        <product_name id="51" name="Blancco Management Console">Blancco Management Console</product_name>
                        <product_version>5.20.0</product_version>
                        <product_revision>302ee2bad746</product_revision>
                    </author>
                    <date>2025-05-15T15:07:24+0900</date>
                    <integrity>gOL64zrek3ixwwf7xaDlTnXm1oMc/AozVpQ9OOWx85OA09LpBfF5chWkQorad5WM6YBHEY9o3fqXnjohwoIu9oqsGLlGWLQwkZk32mIwmNPsx0lo54ZZ6HQAbbzmFEyWBBXS09odoTP2K9PSzKzASVMJP8XJ/nfpOVZOnzKtiYB4U8xcmvWPJmDrsM6g0rgi4LtgSi5dpY1GzIeJPBh0ETdvT1Nyv/wPO27NaaFryfdeXLz1G4P6a+ASFwx18rQO/2joEtpZkuUyLyoLE/rcpiOGmd67GiRrZ+8O34hjC1xdJON+omzvMwKxWo4f2qYHyqSx6s9IyxcY8D9BS+0SuA==</integrity>
                    <key identifier="100"/>
                </log_entry>
            </document_log>
            <entry name="description_entries">
                <entry name="verified" type="string">true</entry>
                <entry name="verified" type="string">false</entry>
                <entry name="verified" type="string">false</entry>
                <entries name="company_information">
                    <entry name="business_name" type="string"></entry>
                    <entry name="business_location" type="string"></entry>
                    <entry name="erasure_provider" type="string"></entry>
                    <entry name="erasure_person" type="string"></entry>
                    <entry name="sync_mode" type="string">Client</entry>
                    <entry name="sync_status" type="string">Failure</entry>
                </entries>
                <entries name="license_consumption_ids">
                    <entry name="license_consumption_id" type="string">57636b0b-69b1-456d-bcb4-6010cdafeed2</entry>
                </entries>
            </entry>
        </description>
        <blancco_erasure_report>
            <entries name="erasures">
                <entry name="erasure_disclaimer" type="string">false</entry>
                <entry name="zebra_disclaimer" type="string">false</entry>
                <entries name="erasure">
                    <entry name="timestamp" type="string">2025-05-13T14:54:33+0900</entry>
                    <entry name="start_time" type="string">2025-05-13 14:54:33</entry>
                    <entry name="end_time" type="string">2025-05-13 15:04:27</entry>
                    <entry name="elapsed_time" type="string">00:09:53</entry>
                    <entry name="erasure_standard_name" type="string">Apple Erasure</entry>
                    <entry name="state" type="string">Successful</entry>
                    <entries name="target">
                        <entry name="type" type="string">Internal memory</entry>
                        <entry name="serial" type="string">C07J50PJQ6NV</entry>
                        <entry name="capacity" type="uint">************</entry>
                        <entry name="vendor" type="string"></entry>
                    </entries>
                </entries>
            </entries>
        </blancco_erasure_report>
        <blancco_hardware_report>
            <entries name="system">
                <entry name="manufacturer" type="string">Apple, Inc.</entry>
                <entry name="name" type="string">Mac mini (M1, 2020)</entry>
                <entry name="market_name" type="string">Mac mini (M1, 2020)</entry>
                <entry name="model" type="string">Mac mini (M1, 2020)</entry>
                <entry name="country_of_origin" type="string">Tech Com, China</entry>
                <entry name="identifier" type="string">Serial:C07J50PJQ6NV</entry>
                <entry name="imei" type="string"></entry>
                <entry name="serial" type="string">C07J50PJQ6NV</entry>
                <entry name="internal_model" type="string">MGNR3</entry>
                <entry name="firmware_version" type="string">15.5</entry>
                <entry name="region" type="string">J/A</entry>
                <entry name="region_code" type="string">J/A</entry>
                <entry name="region_name" type="string">Japan</entry>
                <entry name="uuid" type="string">00008103-000924263A60801E</entry>
                <entry name="ecid" type="string">000924263A60801E</entry>
                <entry name="chassis_type" type="string">Desktop</entry>
                <entry name="mdm_status" type="string">Unknown</entry>
                <entry name="manufacturing_date" type="string">201231</entry>
            </entries>
            <entries name="disks">
                <entries name="disk">
                    <entry name="type" type="string">Internal memory</entry>
                    <entry name="serial" type="string">C07J50PJQ6NV</entry>
                    <entry name="capacity" type="uint">************</entry>
                    <entry name="vendor" type="string"></entry>
                </entries>
            </entries>
        </blancco_hardware_report>
        <blancco_software_report>
            <entries name="operating_system">
                <entry name="name" type="string">macOS</entry>
                <entry name="version" type="string">15.5</entry>
            </entries>
        </blancco_software_report>
    </blancco_data>
    <user_data>
        <entries name="fields">
            <entry name="Comments" type="string"></entry>
            <entry name="TP Number" type="string">JPN3277-349231</entry>
            <entry name="Load_number" type="string">8122906-1</entry>
            <entry name="Comment" type="string">SC,DR,RM: Processor-Apple M1; RAM-8GB; Embedded RAM</entry>
            <entry name="Usertag" type="string">U-3024</entry>
        </entries>
    </user_data>
</report></root>