<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleDisplayName</key>
	<string>VS Mac Tools v2</string>
	<key>CFBundleExecutable</key>
	<string>VS Mac Tools v2</string>
	<key>CFBundleIconFile</key>
	<string>vsmactool.icns</string>
	<key>CFBundleIdentifier</key>
	<string>com.vonzki.vsmactools</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>VS Mac Tools v2</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>2.0.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>2.0.1</string>
	<key>LSHasLocalizedDisplayName</key>
	<false/>
	<key>LSMinimumSystemVersion</key>
	<string>10.15.0</string>
	<key>NSAppleScriptEnabled</key>
	<false/>
	<key>NSHighResolutionCapable</key>
	<true/>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright not specified</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>PyMainFileNames</key>
	<array>
		<string>__boot__</string>
	</array>
	<key>PyOptions</key>
	<dict>
		<key>alias</key>
		<false/>
		<key>argv_emulation</key>
		<false/>
		<key>emulate_shell_environment</key>
		<false/>
		<key>no_chdir</key>
		<false/>
		<key>prefer_ppc</key>
		<false/>
		<key>site_packages</key>
		<false/>
		<key>use_faulthandler</key>
		<false/>
		<key>use_pythonpath</key>
		<false/>
		<key>verbose</key>
		<false/>
	</dict>
	<key>PyResourcePackages</key>
	<array/>
	<key>PyRuntimeLocations</key>
	<array>
		<string>@executable_path/../Frameworks/Python3.framework/Versions/3.9/Python3</string>
	</array>
	<key>PythonInfoDict</key>
	<dict>
		<key>PythonExecutable</key>
		<string>/Library/Developer/CommandLineTools/usr/bin/python3</string>
		<key>PythonLongVersion</key>
		<string>3.9.6 (default, Apr 30 2025, 02:07:17) 
[Clang 17.0.0 (clang-1700.0.13.5)]</string>
		<key>PythonShortVersion</key>
		<string>3.9</string>
		<key>py2app</key>
		<dict>
			<key>alias</key>
			<false/>
			<key>template</key>
			<string>app</string>
			<key>version</key>
			<string>0.28.8</string>
		</dict>
	</dict>
</dict>
</plist>
