<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/base_library.zip</key>
		<data>
		j6MjfWJQrDRximgCfQYHCz9QVEI=
		</data>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/img/bluetooth.icns</key>
		<data>
		BWQueLjvWPlbIq/Kh/iUbQpap6s=
		</data>
		<key>Resources/img/device.icns</key>
		<data>
		sV33xFn17BTEvfFLUBfa2Yv1sxY=
		</data>
		<key>Resources/img/erase.png</key>
		<data>
		9BPaprJeiwNOEmv1HSsD2/Yi7mg=
		</data>
		<key>Resources/img/exit.png</key>
		<data>
		VWk+nE0rFweKNEjgXsZAgVd9rHI=
		</data>
		<key>Resources/img/findmy.icns</key>
		<data>
		EzmzWjuyERvTr2fQDimaOfU3Iqw=
		</data>
		<key>Resources/img/okay/vsmactool.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/okay/vsmactool.png</key>
		<data>
		Uimv9WF+UGRuWUwIbPkhxHYZUXY=
		</data>
		<key>Resources/img/okay/vsmactool2.png</key>
		<data>
		psJjBrDzclYJElJCxSbfMg06bhA=
		</data>
		<key>Resources/img/shutdown.png</key>
		<data>
		K0UjnvC4DqC907tryPgh+wfMy/0=
		</data>
		<key>Resources/img/sysinfo.icns</key>
		<data>
		Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
		</data>
		<key>Resources/img/test/AppIcons.zip</key>
		<data>
		yQytw+KGWjcxQ80CHDdnyeYD0mY=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/1024.png</key>
		<data>
		fi7WUP9bk84QtM0+dthHyeFsthg=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/128.png</key>
		<data>
		oeopB4YSsUAHF6wKiATXtgLIFqM=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/16.png</key>
		<data>
		WrvNy8+N2egPbmDglx7WR6+FqYI=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/256.png</key>
		<data>
		R77D7cmTn2Evfe8NpXrg2bVi3x0=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/32.png</key>
		<data>
		w2EU8YWJtXI9XLXXgHOnoTUIA88=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/512.png</key>
		<data>
		FnjpczOHZg1EmmbclEeePlIC7w8=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/64.png</key>
		<data>
		akLFfRCQXD6Swbt5yDekBY0EaAY=
		</data>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/Contents.json</key>
		<data>
		sL/eylRRl1KU75ZA/IAi6yF3Idk=
		</data>
		<key>Resources/img/test/AppIcons/appstore.png</key>
		<data>
		fi7WUP9bk84QtM0+dthHyeFsthg=
		</data>
		<key>Resources/img/test/AppIcons/playstore.png</key>
		<data>
		FnjpczOHZg1EmmbclEeePlIC7w8=
		</data>
		<key>Resources/img/test/mac1.png</key>
		<data>
		gHkVwf81CqySvH7XRuO3BaoiZCE=
		</data>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		8SU9TvCuDu5FePRUdqXMkButmQA=
		</data>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<data>
		JFvir4nfiG5ijDbsAcYQx5/aljU=
		</data>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<data>
		OwGsAniYvxO5Qk3uKKBarLJ1XzU=
		</data>
		<key>Resources/img/vs_icns/vmt2.png</key>
		<data>
		CLprf1f+Nlm7Xlt8oPUsKGyioHs=
		</data>
		<key>Resources/img/vs_icns/vmt3.png</key>
		<data>
		v3L6NprIlpGaKTt80LNNo+icLKY=
		</data>
		<key>Resources/img/vs_icns/vmt4.png</key>
		<data>
		uvQP7FUxJ8n24MQnlcQiiiQqZzU=
		</data>
		<key>Resources/img/vs_icns/vs1.png</key>
		<data>
		WTy3qC0l5ptb5QEORcAlIreVZG8=
		</data>
		<key>Resources/img/vs_icns/vs2.png</key>
		<data>
		fN+Ic+xtw43hAA2OYq4XMWw60oQ=
		</data>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<data>
		XWVuYXsulSYsBi6hS/M9yWJxTwY=
		</data>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<data>
		Csp0Pn/Bo31hLGWXKSXAnj4PqR0=
		</data>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<data>
		rBNRUBIJKhuwT3L4ycOkP9De1Ew=
		</data>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<data>
		3w08zKeFThN4uhnlb/nlhXnxFuo=
		</data>
		<key>Resources/img/vs_icns/vs_mac_tool_v2.icns</key>
		<data>
		Zuk2Y1dkmvBlWeeGeNW3+9h9l/0=
		</data>
		<key>Resources/img/vs_icns/vs_mac_tool_v2.png</key>
		<data>
		tm8iNmkmZ8VXl/C4cczT2Mh3RD0=
		</data>
		<key>Resources/img/vs_icns/vs_mac_tool_v2_rounded.png</key>
		<data>
		wAMt3GxURS5hENvaxquEIyxAjdE=
		</data>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		e29Oba/lrQE1Wezk7M++DzQGbhc=
		</data>
		<key>Resources/img/vs_icns/vsmactool.png</key>
		<data>
		gssccyBWyzZKOgzo4SDNHdgnYgo=
		</data>
		<key>Resources/img/vsmactool.icns</key>
		<data>
		/bZf6q4HmLDhO0YEfkZNtGl9Frw=
		</data>
		<key>Resources/img/vsmactool.png</key>
		<data>
		nWqWQE/6RLXgjcE53X1jr7QTYzg=
		</data>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<data>
		oBD4j/438pcF8UfpEu8/M3JP/X8=
		</data>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<data>
		DgyO1FNbP2AQLgHJdfq3BLYh2Vo=
		</data>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<data>
		UqlH76EyJ99OUmMBapgim5ct8fU=
		</data>
		<key>Resources/img/xxx/bluetooth.png</key>
		<data>
		9ldniYDf5s5W2rBtESN4G9Q15II=
		</data>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<data>
		boiqyT6aJvpi4JNeYXWVP0JHLNQ=
		</data>
		<key>Resources/img/xxx/checkADE.icns</key>
		<data>
		KBz6o1IJg9OplEnKhcHyPj/T4Vw=
		</data>
		<key>Resources/img/xxx/checkADE.png</key>
		<data>
		lQIica3CIebx69ddnBk4tn9b06k=
		</data>
		<key>Resources/img/xxx/checkADE2.png</key>
		<data>
		eeCm4B12OpiZAoTfNHGtmY7pAro=
		</data>
		<key>Resources/img/xxx/clear.png</key>
		<data>
		ALKnMwBx3+0JyClpaiw6/LZKT/Q=
		</data>
		<key>Resources/img/xxx/device.icns</key>
		<data>
		gFaGbZ06QBxXraWQvzdyyDGGj0E=
		</data>
		<key>Resources/img/xxx/panda.icns</key>
		<data>
		IbLGCBrdrS+78zbZMTa9U0dm6CE=
		</data>
		<key>Resources/img/xxx/panda.ico</key>
		<data>
		QFalV5j9coD4nKk4mAHrAGFDAM4=
		</data>
		<key>Resources/img/xxx/xxx.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
		<key>Resources/img/xxx/xxx.png</key>
		<data>
		QHrz8+tr6hEUo/qttHsBz6W8/pw=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/LICENSE</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/METADATA</key>
		<data>
		nPSbev91rJ4PppQPWjQgj6hwMMo=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/RECORD</key>
		<data>
		o/fcjuVGVBtNUoNKQjjB+3fmKLg=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/WHEEL</key>
		<data>
		pByoQsqfpfXS76mODiPJv8ub9e4=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/top_level.txt</key>
		<data>
		TmH5Jk3nR4O1kkJJvP4bBvF4ua0=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<data>
		BEXtD2mRDuruA28Jo5oTxuHzfhI=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<data>
		WRVfQD62iIV68exEpB8/CT8tAzw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<data>
		f/xJlOQP+lzUzXuvQGzXALlyCDU=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<data>
		8dH5Vh360Fue0WnPyApHzleku2o=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<data>
		iRVBKy2hRNmOZD53OXV7DrKY+sc=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<data>
		+Bx3AhWqVcspD92h48yte3axJNs=
		</data>
		<key>Resources/password_dialog.py</key>
		<data>
		FQw4dffVqjHgTXzPT+LYi4qdmyY=
		</data>
		<key>Resources/tkinter/test/README</key>
		<data>
		zeWBu5/aaPO06CqQtjGNqBzH3Rs=
		</data>
		<key>Resources/vsmactool.icns</key>
		<data>
		/bZf6q4HmLDhO0YEfkZNtGl9Frw=
		</data>
		<key>Resources/xml/05-16 test.xml</key>
		<data>
		oCrSTZl90lrfp0sIezfZ0yEc0l8=
		</data>
		<key>Resources/xml/8123456.xml</key>
		<data>
		dx/ez0+pzADCIoeErZ4xC7+4z4I=
		</data>
		<key>Resources/xml/blancco_report.xml</key>
		<data>
		pGAOUdCdXNCVjEFxbiHXj4Np5dA=
		</data>
		<key>Resources/xml/old_blancco_report.xml</key>
		<data>
		LP6PL3XAtWcanrwlCnxC9gEvk3g=
		</data>
		<key>Resources/xml/reports (1).xml</key>
		<data>
		XbdKR+/YVse8PU3DzelmFigldr8=
		</data>
		<key>Resources/xml/reports.xml</key>
		<data>
		f8plR/Z28+YSBLAp/ubwjwrka7o=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oquNTqBnHpMlz5394zrC17FGi80=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2ab8d4ea0671e9325cf9dfde33ac2d7b1468bcd"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x7R3O7pZUNBEfQAdsl54ix4LaJ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7b4773bba5950d0447d001db25e788b1e0b689e"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			vOar1FXZ7WcuWRqbNA5cYJVqmWw=
			</data>
			<key>requirement</key>
			<string>cdhash H"bce6abd455d9ed672e591a9b340e5c60956a996c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			IEdMZTK9mMXiCDCIQU45IH9mD9E=
			</data>
			<key>requirement</key>
			<string>cdhash H"20474c6532bd98c5e2083088414e39207f660fd1"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8aTGLWPNL8Z0mq/ZCCqo4ITzkTw=
			</data>
			<key>requirement</key>
			<string>cdhash H"f1a4c62d63cd2fc6749aafd9082aa8e084f3913c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			a4upi7rj26KgmwWmOVWseAwGv+8=
			</data>
			<key>requirement</key>
			<string>cdhash H"6b8ba98bbae3dba2a09b05a63955ac780c06bfef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x9bP0OkLtFZ19oat7KODBojVte8=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7d6cfd0e90bb45675f686adeca3830688d5b5ef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			2JRI3oDk/hiPO6KHLr1W3RGSs9g=
			</data>
			<key>requirement</key>
			<string>cdhash H"d89448de80e4fe188f3ba2872ebd56dd1192b3d8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			S9OWwquTmkEKpIFW/jqzt1HHtug=
			</data>
			<key>requirement</key>
			<string>cdhash H"4bd396c2ab939a410aa48156fe3ab3b751c7b6e8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jeRVXzKhN1QeR2XcCm4BMJPTLUE=
			</data>
			<key>requirement</key>
			<string>cdhash H"8de4555f32a137541e4765dc0a6e013093d32d41"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oFtR66WJm5ejzYocVozZR9+7P+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"a05b51eba5899b97a3cd8a1c568cd947dfbb3fe0"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			CA17m/ktNfM9WuMFAkoquWHpDmA=
			</data>
			<key>requirement</key>
			<string>cdhash H"080d7b9bf92d35f33d5ae305024a2ab961e90e60"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1NCCXcJJRfMo8FBrOsPdJwbRoWQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4d0825dc24945f328f0506b3ac3dd2706d1a164"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DA9qe8wlu/qHWZWmnl6S9M/Yndk=
			</data>
			<key>requirement</key>
			<string>cdhash H"0c0f6a7bcc25bbfa875995a69e5e92f4cfd89dd9"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ud90uzcjWeDAVJwDgJ6hgN65Sy0=
			</data>
			<key>requirement</key>
			<string>cdhash H"51df74bb372359e0c0549c03809ea180deb94b2d"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LsusOeFUh1notacct2CcfQQPznM=
			</data>
			<key>requirement</key>
			<string>cdhash H"2ecbac39e1548759e8b5a71cb7609c7d040fce73"</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Aphb79tmqTxGnxAxWwWYRMZHhJo=
			</data>
			<key>requirement</key>
			<string>cdhash H"02985befdb66a93c469f10315b059844c647849a"</string>
		</dict>
		<key>Frameworks/Python3</key>
		<dict>
			<key>symlink</key>
			<string>Python3.framework/Versions/3.9/Python3</string>
		</dict>
		<key>Frameworks/Python3.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			xfccs665Mvtbx0rTtO+URkqYpiw=
			</data>
			<key>requirement</key>
			<string>cdhash H"c5f71cb3aeb932fb5bc74ad3b4ef94464a98a62c"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/customtkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter</string>
		</dict>
		<key>Frameworks/img</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/img</string>
		</dict>
		<key>Frameworks/importlib_metadata-8.6.1.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/importlib_metadata-8.6.1.dist-info</string>
		</dict>
		<key>Frameworks/keyring-25.6.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/keyring-25.6.0.dist-info</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			tOb7dQTf6C4SgXjFrdii0YdoH98=
			</data>
			<key>requirement</key>
			<string>cdhash H"b4e6fb7504dfe82e128178c5add8a2d187681fdf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oLFHeppZt74mUcf28+znvBaZhS4=
			</data>
			<key>requirement</key>
			<string>cdhash H"a0b1477a9a59b7be2651c7f6f3ece7bc1699852e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			2ZggNr90cMKTpZdKIohz0LAbIxA=
			</data>
			<key>requirement</key>
			<string>cdhash H"d9982036bf7470c293a5974a228873d0b01b2310"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gxIMJtc5FrnDgVYyWqGa+qrXCvI=
			</data>
			<key>requirement</key>
			<string>cdhash H"83120c26d73916b9c38156325aa19afaaad70af2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			4I0qigfTbxMMoryy/320d1DJWrA=
			</data>
			<key>requirement</key>
			<string>cdhash H"e08d2a8a07d36f130ca2bcb2ff7db47750c95ab0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			w8bd0HY48RR2pxJlumKpBiSml0o=
			</data>
			<key>requirement</key>
			<string>cdhash H"c3c6ddd07638f11476a71265ba62a90624a6974a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Tjk+5/cgXDCQHbNcte8FbhJjUew=
			</data>
			<key>requirement</key>
			<string>cdhash H"4e393ee7f7205c30901db35cb5ef056e126351ec"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lnS/WH/5FnvkQ/L74dFZRh+77/4=
			</data>
			<key>requirement</key>
			<string>cdhash H"9674bf587ff9167be443f2fbe1d159461fbbeffe"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gpQCVNJ0ugJD92hClU2/M8myqL4=
			</data>
			<key>requirement</key>
			<string>cdhash H"82940254d274ba0243f76842954dbf33c9b2a8be"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rWdLsbDRMVOasWha4i95JZbRNYA=
			</data>
			<key>requirement</key>
			<string>cdhash H"ad674bb1b0d131539ab1685ae22f792596d13580"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			g/Wwb7Mhden6vEvWFnWzyQdMzP0=
			</data>
			<key>requirement</key>
			<string>cdhash H"83f5b06fb32175e9fabc4bd61675b3c9074cccfd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LstjFvzqiQheAB6bjOT4NfkwO7E=
			</data>
			<key>requirement</key>
			<string>cdhash H"2ecb6316fcea89085e001e9b8ce4f835f9303bb1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hdj9PgdnGmN111BEIgrdgIzWasQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"85d8fd3e07671a6375d75044220add808cd66ac4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9b2VjtMsepXEZzK37qBtVf9Xugc=
			</data>
			<key>requirement</key>
			<string>cdhash H"f5bd958ed32c7a95c46732b7eea06d55ff57ba07"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			z+nnfJso+3S3G4jvB0OfOiIT2lk=
			</data>
			<key>requirement</key>
			<string>cdhash H"cfe9e77c9b28fb74b71b88ef07439f3a2213da59"</string>
		</dict>
		<key>Frameworks/lib-dynload/_elementtree.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dAJxzV/ViKuOfokJ7bEurTG/79Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"740271cd5fd588ab8e7e8909edb12ead31bfefd4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			49dCHqx3E7qcP7AZks68v2j9eJ8=
			</data>
			<key>requirement</key>
			<string>cdhash H"e3d7421eac7713ba9c3fb01992cebcbf68fd789f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			uGeiQ3WFbtWbyzYGRkeBiJXzAME=
			</data>
			<key>requirement</key>
			<string>cdhash H"b867a24375856ed59bcb36064647818895f300c1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gCLFJ2RC1Ybo75EZ5KkXcIdMNdA=
			</data>
			<key>requirement</key>
			<string>cdhash H"8022c5276442d586e8ef9119e4a91770874c35d0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Vko8YBVAUokVbjLhZ1clftuR4Bk=
			</data>
			<key>requirement</key>
			<string>cdhash H"564a3c6015405289156e32e16757257edb91e019"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pVZ5L8i+J4G+NgOySvymWCMSIDc=
			</data>
			<key>requirement</key>
			<string>cdhash H"a556792fc8be2781be3603b24afca65823122037"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			J8YzMjuLivaeTqB2fH9qgGOIV80=
			</data>
			<key>requirement</key>
			<string>cdhash H"27c633323b8b8af69e4ea0767c7f6a80638857cd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			H71fWIo/km7Um9B05wqqP7XtjVs=
			</data>
			<key>requirement</key>
			<string>cdhash H"1fbd5f588a3f926ed49bd074e70aaa3fb5ed8d5b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5II0IusaKTTPg6pqCfsnVwjtDVg=
			</data>
			<key>requirement</key>
			<string>cdhash H"e4823422eb1a2934cf83aa6a09fb275708ed0d58"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			j6e0Oc2A2kGHizBg6LoRpUuv1Vc=
			</data>
			<key>requirement</key>
			<string>cdhash H"8fa7b439cd80da41878b3060e8ba11a54bafd557"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			xKfHOBdR+ukN4ru7r7pKrIDd+2s=
			</data>
			<key>requirement</key>
			<string>cdhash H"c4a7c7381751fae90de2bbbbafba4aac80ddfb6b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			76PVZWJLa9Dzg1RggXL86SVGrv0=
			</data>
			<key>requirement</key>
			<string>cdhash H"efa3d565624b6bd0f38354608172fce92546aefd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JLtqMUr2n7ve382hdVQl3HzvOZ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"24bb6a314af69fbbdedfcda1755425dc7cef399e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			orqIoN0gfBGeTLx7E0UDNFag4L8=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2ba88a0dd207c119e4cbc7b1345033456a0e0bf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Inxn4N86qQ4GSPUvE5y0+2q0Vp4=
			</data>
			<key>requirement</key>
			<string>cdhash H"227c67e0df3aa90e0648f52f139cb4fb6ab4569e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			d7RZJBLPO5BBIdAeL4eRZ66b4p8=
			</data>
			<key>requirement</key>
			<string>cdhash H"77b4592412cf3b904121d01e2f879167ae9be29f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha256.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wWUiVqAobe/qSSSxfKhIxQzbyBA=
			</data>
			<key>requirement</key>
			<string>cdhash H"c1652256a0286defea4924b17ca848c50cdbc810"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oczwGtfnmXLZke5eV6L82jKu9MU=
			</data>
			<key>requirement</key>
			<string>cdhash H"a1ccf01ad7e79972d991ee5e57a2fcda32aef4c5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha512.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			V82aDE5pmrjd84lfYFZkAe9yEDo=
			</data>
			<key>requirement</key>
			<string>cdhash H"57cd9a0c4e699ab8ddf3895f60566401ef72103a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kDcuSqwI0/qdwZx0iqo+7x5UhtU=
			</data>
			<key>requirement</key>
			<string>cdhash H"90372e4aac08d3fa9dc19c748aaa3eef1e5486d5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			AxQt4QLFuFjSwjQQkF6lSuNi/Ds=
			</data>
			<key>requirement</key>
			<string>cdhash H"03142de102c5b858d2c23410905ea54ae362fc3b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OlIekPZHjyV0zoUONXfnj1qfeSc=
			</data>
			<key>requirement</key>
			<string>cdhash H"3a521e90f6478f2574ce850e3577e78f5a9f7927"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1NrYIRSaMlWPADbiLXzOFBDmDbI=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4dad821149a32558f0036e22d7cce1410e60db2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_tkinter.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3KjFCZbcFxDmKahk39ZcpQ3nHSE=
			</data>
			<key>requirement</key>
			<string>cdhash H"dca8c50996dc1710e629a864dfd65ca50de71d21"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GCDMA/vB5UV/Y3PNhNyNX9Y5Blw=
			</data>
			<key>requirement</key>
			<string>cdhash H"1820cc03fbc1e5457f6373cd84dc8d5fd639065c"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rRRGjdOAIGReLPtVnRQcFbDL7Lg=
			</data>
			<key>requirement</key>
			<string>cdhash H"ad14468dd38020645e2cfb559d141c15b0cbecb8"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SnfOQE602PxkXU8Sqsqt/qvG0n8=
			</data>
			<key>requirement</key>
			<string>cdhash H"4a77ce404eb4d8fc645d4f12aacaadfeabc6d27f"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DPffwejekKqrtM58HDLir8rzDmE=
			</data>
			<key>requirement</key>
			<string>cdhash H"0cf7dfc1e8de90aaabb4ce7c1c32e2afcaf30e61"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			utebxTBPq72YVrOZYSp1hlP5zQo=
			</data>
			<key>requirement</key>
			<string>cdhash H"bad79bc5304fabbd9856b399612a758653f9cd0a"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5kjr2ePoZS2d3aF2cJCvQ+JmLqM=
			</data>
			<key>requirement</key>
			<string>cdhash H"e648ebd9e3e8652d9ddda1767090af43e2662ea3"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Kn1KfyMUnTvzUeQUgQ67B1w9qmI=
			</data>
			<key>requirement</key>
			<string>cdhash H"2a7d4a7f23149d3bf351e414810ebb075c3daa62"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LNNPP7H9dr7Y7xVkqL4WFUEqLJc=
			</data>
			<key>requirement</key>
			<string>cdhash H"2cd34f3fb1fd76bed8ef1564a8be1615412a2c97"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			vVAhejDVyYVHVqUE4krkBq5Bv58=
			</data>
			<key>requirement</key>
			<string>cdhash H"bd50217a30d5c9854756a504e24ae406ae41bf9f"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			FVX0CHY5twXsbA4oiAkcHZ9b1rs=
			</data>
			<key>requirement</key>
			<string>cdhash H"1555f4087639b705ec6c0e2888091c1d9f5bd6bb"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			nlLgVBZ+FEWAyz64zo78mbfKy5I=
			</data>
			<key>requirement</key>
			<string>cdhash H"9e52e054167e144580cb3eb8ce8efc99b7cacb92"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ipinocrWP1531ykfVFwUcYsPSVM=
			</data>
			<key>requirement</key>
			<string>cdhash H"8a98a7a1cad63f5e77d7291f545c14718b0f4953"</string>
		</dict>
		<key>Frameworks/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Frameworks/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Frameworks/numpy/_core/_multiarray_tests.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QQqEtMeF5G1tYM/BPink26awOUU=
			</data>
			<key>requirement</key>
			<string>cdhash H"410a84b4c785e46d6d60cfc13e29e4dba6b03945"</string>
		</dict>
		<key>Frameworks/numpy/_core/_multiarray_umath.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dNF9QjOQvQj+1jchiE65yJBcEOQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"74d17d423390bd08fed63721884eb9c8905c10e4"</string>
		</dict>
		<key>Frameworks/numpy/fft/_pocketfft_umath.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			CzJMjZwx19/jGTOEU5q939e+ZYc=
			</data>
			<key>requirement</key>
			<string>cdhash H"0b324c8d9c31d7dfe3193384539abddfd7be6587"</string>
		</dict>
		<key>Frameworks/numpy/linalg/_umath_linalg.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hpVOBrRICw5M1+D1YNifHF1wUu0=
			</data>
			<key>requirement</key>
			<string>cdhash H"86954e06b4480b0e4cd7e0f560d89f1c5d7052ed"</string>
		</dict>
		<key>Frameworks/numpy/random/_bounded_integers.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UkeHaDphtY48UVzZWIngCKHxv2E=
			</data>
			<key>requirement</key>
			<string>cdhash H"524787683a61b58e3c515cd95889e008a1f1bf61"</string>
		</dict>
		<key>Frameworks/numpy/random/_common.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gPVCKjXsS8VlVcKBUi1QgiY9PBE=
			</data>
			<key>requirement</key>
			<string>cdhash H"80f5422a35ec4bc56555c281522d5082263d3c11"</string>
		</dict>
		<key>Frameworks/numpy/random/_generator.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oZaMChGh9hlgFyZG6oJV5tOdRrw=
			</data>
			<key>requirement</key>
			<string>cdhash H"a1968c0a11a1f61960172646ea8255e6d39d46bc"</string>
		</dict>
		<key>Frameworks/numpy/random/_mt19937.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6bb1vVfEpjujG6ixv/CHvXeNgC8=
			</data>
			<key>requirement</key>
			<string>cdhash H"e9b6f5bd57c4a63ba31ba8b1bff087bd778d802f"</string>
		</dict>
		<key>Frameworks/numpy/random/_pcg64.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			TuWStm/4Umscq2jm3UgVN233IVE=
			</data>
			<key>requirement</key>
			<string>cdhash H"4ee592b66ff8526b1cab68e6dd4815376df72151"</string>
		</dict>
		<key>Frameworks/numpy/random/_philox.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MZfapYpo0sGpe/X+6VLlb/4bvsQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"3197daa58a68d2c1a97bf5fee952e56ffe1bbec4"</string>
		</dict>
		<key>Frameworks/numpy/random/_sfc64.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OD6xFll63VvaNWLiyO7E1/CwPOc=
			</data>
			<key>requirement</key>
			<string>cdhash H"383eb116597add5bda3562e2c8eec4d7f0b03ce7"</string>
		</dict>
		<key>Frameworks/numpy/random/bit_generator.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KA6Prpqqfi3HvpdzcXVpwMbBFdw=
			</data>
			<key>requirement</key>
			<string>cdhash H"280e8fae9aaa7e2dc7be9773717569c0c6c115dc"</string>
		</dict>
		<key>Frameworks/numpy/random/mtrand.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			65pgsJvIPMbRg1TJyia33k+FiVg=
			</data>
			<key>requirement</key>
			<string>cdhash H"eb9a60b09bc83cc6d18354c9ca26b7de4f858958"</string>
		</dict>
		<key>Frameworks/password_dialog.py</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/password_dialog.py</string>
		</dict>
		<key>Frameworks/tkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/tkinter</string>
		</dict>
		<key>Frameworks/xml</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/xml</string>
		</dict>
		<key>Resources/PIL</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/PIL</string>
		</dict>
		<key>Resources/Python3</key>
		<dict>
			<key>symlink</key>
			<string>Python3.framework/Versions/3.9/Python3</string>
		</dict>
		<key>Resources/Python3.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python3.framework</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			7rGlNRGZawnoQV0BqOGXst0AL9zU5oFbEI5tZGRqKAQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/img/bluetooth.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			AXDfQ7PFxvX7gGQYsojZ3L4PMUU0IIMf0Vbora632Mc=
			</data>
		</dict>
		<key>Resources/img/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			/wu7jVkhEKGPoKTYum8zEAUTPQuFfFMXP41QTSed88g=
			</data>
		</dict>
		<key>Resources/img/erase.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7l2sY7T3qXSjQZXIEDtzavqbz2NSrlH4itxtn6/igJc=
			</data>
		</dict>
		<key>Resources/img/exit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b5d6vlIoz/sJxS4j+nsUsX46dyNReM1kJkVz+QSIJJ8=
			</data>
		</dict>
		<key>Resources/img/findmy.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			6O5TLJgtkHq7LHYtkdu2JJQppDEmUUKnPrSZurSZ3Ew=
			</data>
		</dict>
		<key>Resources/img/okay/vsmactool.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/okay/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2V96COlhVUrjv1CRdLPB+dFuRLsubZcZaJJBfUus4nQ=
			</data>
		</dict>
		<key>Resources/img/okay/vsmactool2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			urcpq5ZoTfeT9FgG4TwlDhtBptqPxzhovOmYHI/n2jY=
			</data>
		</dict>
		<key>Resources/img/shutdown.png</key>
		<dict>
			<key>hash2</key>
			<data>
			adYIbIXC8nbpXDrEmKQUQo+zPZY5R8nBZJbzWaxWFPc=
			</data>
		</dict>
		<key>Resources/img/sysinfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			vZn9xKNKjSY5I0arOZQ4oRzT4WN8azNInwkwJN8dE2k=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			aPWc8A00KS4pf8e93j+025bVLEA1MBv3iOIGf59/P+A=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/1024.png</key>
		<dict>
			<key>hash2</key>
			<data>
			leqc+S3L5u+nSGkAWiJSEPQsBDDxwEu8cHLtv4XCeVw=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/128.png</key>
		<dict>
			<key>hash2</key>
			<data>
			u2VRND6M0tdmSiHDRHna/toU9I22GCFE7pkjw6IvQb0=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/16.png</key>
		<dict>
			<key>hash2</key>
			<data>
			dxrMsX3yRCxdrROCBtwKA5Orcsv5FGEAl0VlEG7oy3s=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/256.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b/d25wC6m88to6ssKTdtok1T3+2VJ+KWmW+odGbJKQo=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Lg3prbFAUS2LMvJHBTw074hQVbGsL03IqvWBrSjGsF8=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/512.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CFwqyC1+tXNjAvT9BqPoubS/0wlr2/ACptdh0peLzYc=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/64.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lhcHljjWVSmev3L4ZtdTkp268XHw6jT6Ef+SMCtzqGQ=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/Assets.xcassets/AppIcon.appiconset/Contents.json</key>
		<dict>
			<key>hash2</key>
			<data>
			a8cjvkswva1c20VbUGN6O8NBlZsO+RnVQ+3LSG/m1ps=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/appstore.png</key>
		<dict>
			<key>hash2</key>
			<data>
			leqc+S3L5u+nSGkAWiJSEPQsBDDxwEu8cHLtv4XCeVw=
			</data>
		</dict>
		<key>Resources/img/test/AppIcons/playstore.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CFwqyC1+tXNjAvT9BqPoubS/0wlr2/ACptdh0peLzYc=
			</data>
		</dict>
		<key>Resources/img/test/mac1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/W/UiSVnmsLy15htxAN81k5no4EdhrzqWz+iCynVLRk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash2</key>
			<data>
			rNQktSPlSyE+VKfSgMGXqpOZTyTJd6h4JNlIUpdDoyA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			WqaKLAzi7FB7Ewbroi4ebjBga54GoM94tWkm7qx3p90=
			</data>
		</dict>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			tjT6ORXV56DHmCh07PXANHtyqJKen6F6ymXv2Z5QlMk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vmt2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4tQNe6UJM9x0ErqEsv+KjZc2XFQ862q3TRtoRReJC34=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vmt3.png</key>
		<dict>
			<key>hash2</key>
			<data>
			AYpJ9bqq/THU+Z4qwBBDi3v/yEMKTBa/qkv9YXT9E2g=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vmt4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Z+9QI12KB/6hF5XKxLEa4cOc8Wnmo3UmrJIfxOJpw4o=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GeEl6CBzyDu50Hu4C2j+XLVl/4jQbIC0t3SlJyK7BsA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1ju/nmVj5Szaqn5wjFVQo4KMWcXB/CjOpYGSfMgk4Kk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			sTzAaJlRqYcg0y5qmy5xwuyQ4gRCnPggVcJIdWnX8i4=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			FJBSpYLuVVrMudR2GNQ0yVHsWgz64STZc5AUwJRJOOU=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			He/b6Cc/DD4cLlU4K4nqPm3/r1AILuwNskrylo3JHbc=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3XK2IiySOO3EzgPGPNv7hpDmm7gIKTs1mkCU7SSaGAg=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			Wk6NcDzc61n+S7/O+JSQG4ZkNPLWS8O+iUcR9c4+arw=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_mac_tool_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CD+ufxpcMTBVE8LJvKzniszXAhS8xNABGEo6MHqbwPU=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_mac_tool_v2_rounded.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iecaM5vNBiUb+RG27Jy3/GUEgorL3Cd8XVY6gYkRcFc=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash2</key>
			<data>
			NpKcDDxjT73l8z1/Nc1X0EFty0BKBNJ7s7LIbSUh3r8=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jOZag2Q3yCpPYvFFXgOuK2Baf8a+HUylmWUhBDhBI6Q=
			</data>
		</dict>
		<key>Resources/img/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lFOvWLdflW1tU5HMS0wAl+vrdmwSApw2D5zRlEO8wzE=
			</data>
		</dict>
		<key>Resources/img/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			T6gwnbchNQ4tJnLwNrEfUyvbOCLS0b4Y4XuTlCJLB7U=
			</data>
		</dict>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			yOfFn7nhq1viUuO2rlX5hyia0vArQ9JV/8opaAICgTA=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			7NPvIdku/VrKm7yv+ITV0DWiNJ8o/9GzDdwNyBPR31c=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			rblFkpNuBhHlkhAnZmha0YXeMvlvn59Z1WHiRV8i9oA=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth.png</key>
		<dict>
			<key>hash2</key>
			<data>
			n+3wBQtqTyxoYteALhMKiZcHUdA1B07ssRXWRE88dDQ=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8IWgML4+zBqL3Is226iqcU6EUotB/sTfNaJ6SfeMef8=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			1V1jeQug46T0y08D/mYq79KveXSvkHV559jV7HKjgF0=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XsQmYdvHwyFU0dtXw2caSJKrbExuSwrxFFwUrvUeCyU=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OEJKmlGH50iAIpFiklwgppToeAxlRc93d70whg30LxA=
			</data>
		</dict>
		<key>Resources/img/xxx/clear.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JMy6eOPBIeGPyt7CJb0POj5Gk/27KHY/7WH+EncDbgo=
			</data>
		</dict>
		<key>Resources/img/xxx/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			EVrTPOkfu3b6zVGgdbFJqeL4YSUjlAaHNBfGUMoNb8w=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			LDnYsUI7Tbe+oZvhEh+36wBZLpo3JsKIUU8tq3vcx1s=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			8s+4K1yGSugGA3D+rfgAcSIaVxl7b1YT+GN+tiwKA1A=
			</data>
		</dict>
		<key>Resources/img/xxx/xxx.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
		<key>Resources/img/xxx/xxx.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rrsBqmDhsnewSY7OzcdCmv3LFV8XTgh4kDnyoL33s28=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			F24ATbamOm1kp40IQGodtMLWMrH+fVEksdySlZk/KAY=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			EivOUoP6j+Ma5YgN8rkYfByAT5kDvj+M0k7w4zaCPq8=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			In9FTNxeP60KnTkGw7wk6mJPYd/dQSjEZmXdBdMCI+8=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CO3fD9yylANiXkrMo4qHLV/mqXL2sC5JFKgt1yWAT+A=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			htoPAa6uRjSKPD1GUZXcHOzN55956HdppkuNoEsqR0E=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			rS2xXSTtNujJISr88RqlBhI4T0Wvx4yWyQLTSGSZ2tM=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			tELbomrvRCwC/hqSzdODz4+yUVa5u2Ymw7NFa2N7lPk=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			PZUExdf71Ui/so67QXpySuHtCi3+J3wvF4ORK6k/S8U=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			8ibyc9zH2ST1JDZHWlQZHEUPx9kVaXfVy8z5af/6OUk=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ohh1dke28/NdSNkZ6nkVSwIKkLJTOwIfEwnXKva3pkg=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Resources/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Resources/numpy</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/numpy</string>
		</dict>
		<key>Resources/password_dialog.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jKoWHr+PIsGwZpLlIzCb5FJrrX+1xVN485fkgdO4D80=
			</data>
		</dict>
		<key>Resources/tkinter/test/README</key>
		<dict>
			<key>hash2</key>
			<data>
			/jx51dqGFso396nY/dqsLJFktZPHsRZYCqmWkKX1mrU=
			</data>
		</dict>
		<key>Resources/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lFOvWLdflW1tU5HMS0wAl+vrdmwSApw2D5zRlEO8wzE=
			</data>
		</dict>
		<key>Resources/xml/05-16 test.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			ETMDoHKhN8rUPDvfDwP/rQ/Gq80briAr5oiqYqfGk6s=
			</data>
		</dict>
		<key>Resources/xml/8123456.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			tfu8h4Qn7pR/W6uPxY0CnBth/JI9OUvU4jUF0ITj1SI=
			</data>
		</dict>
		<key>Resources/xml/blancco_report.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			+KJqwa6ljhsgBZnHQrq3DX6weuzUeh3k6dzAvu5ag40=
			</data>
		</dict>
		<key>Resources/xml/old_blancco_report.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			Ysdy6M0Sk1T2K0FVngnngSBC7IIm9+Q88znIGeJtsn4=
			</data>
		</dict>
		<key>Resources/xml/reports (1).xml</key>
		<dict>
			<key>hash2</key>
			<data>
			5cefcTdr8ZTk28szPsu9MUOaQ1m9byeo/ZwzehmJjn8=
			</data>
		</dict>
		<key>Resources/xml/reports.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			vWj484ytl9qRKVB8o0fa8yC9LzefN06omu4twep4iLM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
