<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/base_library.zip</key>
		<data>
		OFx2bDSJU+ngf88939BgORa2As8=
		</data>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/img/123.webp</key>
		<data>
		tU9ln24m/WldMzrodXCCV+zrzeE=
		</data>
		<key>Resources/img/bluetooth.icns</key>
		<data>
		BWQueLjvWPlbIq/Kh/iUbQpap6s=
		</data>
		<key>Resources/img/device.icns</key>
		<data>
		sV33xFn17BTEvfFLUBfa2Yv1sxY=
		</data>
		<key>Resources/img/erase.png</key>
		<data>
		9BPaprJeiwNOEmv1HSsD2/Yi7mg=
		</data>
		<key>Resources/img/exit.png</key>
		<data>
		VWk+nE0rFweKNEjgXsZAgVd9rHI=
		</data>
		<key>Resources/img/findmy.icns</key>
		<data>
		EzmzWjuyERvTr2fQDimaOfU3Iqw=
		</data>
		<key>Resources/img/shutdown.png</key>
		<data>
		K0UjnvC4DqC907tryPgh+wfMy/0=
		</data>
		<key>Resources/img/sysinfo.icns</key>
		<data>
		Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
		</data>
		<key>Resources/img/vmt4.png</key>
		<data>
		uvQP7FUxJ8n24MQnlcQiiiQqZzU=
		</data>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		8SU9TvCuDu5FePRUdqXMkButmQA=
		</data>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<data>
		JFvir4nfiG5ijDbsAcYQx5/aljU=
		</data>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<data>
		OwGsAniYvxO5Qk3uKKBarLJ1XzU=
		</data>
		<key>Resources/img/vs_icns/vmt2.png</key>
		<data>
		CLprf1f+Nlm7Xlt8oPUsKGyioHs=
		</data>
		<key>Resources/img/vs_icns/vmt3.png</key>
		<data>
		v3L6NprIlpGaKTt80LNNo+icLKY=
		</data>
		<key>Resources/img/vs_icns/vs1.png</key>
		<data>
		WTy3qC0l5ptb5QEORcAlIreVZG8=
		</data>
		<key>Resources/img/vs_icns/vs2.png</key>
		<data>
		fN+Ic+xtw43hAA2OYq4XMWw60oQ=
		</data>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<data>
		XWVuYXsulSYsBi6hS/M9yWJxTwY=
		</data>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<data>
		Csp0Pn/Bo31hLGWXKSXAnj4PqR0=
		</data>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<data>
		rBNRUBIJKhuwT3L4ycOkP9De1Ew=
		</data>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<data>
		3w08zKeFThN4uhnlb/nlhXnxFuo=
		</data>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		e29Oba/lrQE1Wezk7M++DzQGbhc=
		</data>
		<key>Resources/img/vs_mac_tool_v2.icns</key>
		<data>
		Zuk2Y1dkmvBlWeeGeNW3+9h9l/0=
		</data>
		<key>Resources/img/vs_mac_tool_v2.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/vs_mac_tool_v2.png</key>
		<data>
		tm8iNmkmZ8VXl/C4cczT2Mh3RD0=
		</data>
		<key>Resources/img/vs_mac_tool_v2_rounded.png</key>
		<data>
		wAMt3GxURS5hENvaxquEIyxAjdE=
		</data>
		<key>Resources/img/vs_mac_tool_v2_temp.png</key>
		<data>
		Uimv9WF+UGRuWUwIbPkhxHYZUXY=
		</data>
		<key>Resources/img/vsmactool.png</key>
		<data>
		gssccyBWyzZKOgzo4SDNHdgnYgo=
		</data>
		<key>Resources/img/xxx.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
		<key>Resources/img/xxx.png</key>
		<data>
		gBENmg8aOCjcY+1IYvKYPIcz/z0=
		</data>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<data>
		oBD4j/438pcF8UfpEu8/M3JP/X8=
		</data>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<data>
		DgyO1FNbP2AQLgHJdfq3BLYh2Vo=
		</data>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<data>
		UqlH76EyJ99OUmMBapgim5ct8fU=
		</data>
		<key>Resources/img/xxx/bluetooth.png</key>
		<data>
		9ldniYDf5s5W2rBtESN4G9Q15II=
		</data>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<data>
		boiqyT6aJvpi4JNeYXWVP0JHLNQ=
		</data>
		<key>Resources/img/xxx/checkADE.icns</key>
		<data>
		KBz6o1IJg9OplEnKhcHyPj/T4Vw=
		</data>
		<key>Resources/img/xxx/checkADE.png</key>
		<data>
		lQIica3CIebx69ddnBk4tn9b06k=
		</data>
		<key>Resources/img/xxx/checkADE2.png</key>
		<data>
		eeCm4B12OpiZAoTfNHGtmY7pAro=
		</data>
		<key>Resources/img/xxx/clear.png</key>
		<data>
		ALKnMwBx3+0JyClpaiw6/LZKT/Q=
		</data>
		<key>Resources/img/xxx/device.icns</key>
		<data>
		gFaGbZ06QBxXraWQvzdyyDGGj0E=
		</data>
		<key>Resources/img/xxx/panda.icns</key>
		<data>
		IbLGCBrdrS+78zbZMTa9U0dm6CE=
		</data>
		<key>Resources/img/xxx/panda.ico</key>
		<data>
		QFalV5j9coD4nKk4mAHrAGFDAM4=
		</data>
		<key>Resources/img/xxx/xxx.png</key>
		<data>
		QHrz8+tr6hEUo/qttHsBz6W8/pw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<data>
		BEXtD2mRDuruA28Jo5oTxuHzfhI=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<data>
		WRVfQD62iIV68exEpB8/CT8tAzw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<data>
		zzt6wSM8yOwQMrLxzwGUVORMeF0=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<data>
		8dH5Vh360Fue0WnPyApHzleku2o=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<data>
		iRVBKy2hRNmOZD53OXV7DrKY+sc=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<data>
		+Bx3AhWqVcspD92h48yte3axJNs=
		</data>
		<key>Resources/password_dialog.py</key>
		<data>
		FQw4dffVqjHgTXzPT+LYi4qdmyY=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<data>
		6Mgw2LCUIwDHyHs7j9FeoTluB70=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<data>
		s5THrsFYNQuvZ2rjGXvvTXFYsxw=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<data>
		eCm0Mku1QnmUlBMaJw7Dva1N7e8=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<data>
		TmH5Jk3nR4O1kkJJvP4bBvF4ua0=
		</data>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<data>
		eDY2ciZNnNP3LVwdNmXhZXsaUHE=
		</data>
		<key>Resources/vs_mac_tool_v2.icns</key>
		<data>
		Zuk2Y1dkmvBlWeeGeNW3+9h9l/0=
		</data>
		<key>Resources/xml/05-16 test.xml</key>
		<data>
		oCrSTZl90lrfp0sIezfZ0yEc0l8=
		</data>
		<key>Resources/xml/8123456.xml</key>
		<data>
		dx/ez0+pzADCIoeErZ4xC7+4z4I=
		</data>
		<key>Resources/xml/blancco_report.xml</key>
		<data>
		pGAOUdCdXNCVjEFxbiHXj4Np5dA=
		</data>
		<key>Resources/xml/old_blancco_report.xml</key>
		<data>
		LP6PL3XAtWcanrwlCnxC9gEvk3g=
		</data>
		<key>Resources/xml/reports (1).xml</key>
		<data>
		XbdKR+/YVse8PU3DzelmFigldr8=
		</data>
		<key>Resources/xml/reports.xml</key>
		<data>
		f8plR/Z28+YSBLAp/ubwjwrka7o=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oquNTqBnHpMlz5394zrC17FGi80=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2ab8d4ea0671e9325cf9dfde33ac2d7b1468bcd"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x7R3O7pZUNBEfQAdsl54ix4LaJ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7b4773bba5950d0447d001db25e788b1e0b689e"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			vOar1FXZ7WcuWRqbNA5cYJVqmWw=
			</data>
			<key>requirement</key>
			<string>cdhash H"bce6abd455d9ed672e591a9b340e5c60956a996c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			IEdMZTK9mMXiCDCIQU45IH9mD9E=
			</data>
			<key>requirement</key>
			<string>cdhash H"20474c6532bd98c5e2083088414e39207f660fd1"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8aTGLWPNL8Z0mq/ZCCqo4ITzkTw=
			</data>
			<key>requirement</key>
			<string>cdhash H"f1a4c62d63cd2fc6749aafd9082aa8e084f3913c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			a4upi7rj26KgmwWmOVWseAwGv+8=
			</data>
			<key>requirement</key>
			<string>cdhash H"6b8ba98bbae3dba2a09b05a63955ac780c06bfef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x9bP0OkLtFZ19oat7KODBojVte8=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7d6cfd0e90bb45675f686adeca3830688d5b5ef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			2JRI3oDk/hiPO6KHLr1W3RGSs9g=
			</data>
			<key>requirement</key>
			<string>cdhash H"d89448de80e4fe188f3ba2872ebd56dd1192b3d8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			S9OWwquTmkEKpIFW/jqzt1HHtug=
			</data>
			<key>requirement</key>
			<string>cdhash H"4bd396c2ab939a410aa48156fe3ab3b751c7b6e8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jeRVXzKhN1QeR2XcCm4BMJPTLUE=
			</data>
			<key>requirement</key>
			<string>cdhash H"8de4555f32a137541e4765dc0a6e013093d32d41"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oFtR66WJm5ejzYocVozZR9+7P+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"a05b51eba5899b97a3cd8a1c568cd947dfbb3fe0"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			CA17m/ktNfM9WuMFAkoquWHpDmA=
			</data>
			<key>requirement</key>
			<string>cdhash H"080d7b9bf92d35f33d5ae305024a2ab961e90e60"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fftQYD2+DMiBrRr160bY3mhKBgo=
			</data>
			<key>requirement</key>
			<string>cdhash H"7dfb50603dbe0cc881ad1af5eb46d8de684a060a"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+hFMLJChFuwmZithzd9kIWZthwE=
			</data>
			<key>requirement</key>
			<string>cdhash H"fa114c2c90a116ec26662b61cddf6421666d8701"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			H8bKCo8qCn9xFZHoP8rFEEz+ucw=
			</data>
			<key>requirement</key>
			<string>cdhash H"1fc6ca0a8f2a0a7f711591e83fcac5104cfeb9cc"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			e4fne8x04eMcg+1EIFPRoe0u9lU=
			</data>
			<key>requirement</key>
			<string>cdhash H"7b87e77bcc74e1e31c83ed442053d1a1ed2ef655"</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6B66VAUB4KMTpLFZNwLGeb+VU3I=
			</data>
			<key>requirement</key>
			<string>cdhash H"e81eba540501e0a313a4b1593702c679bf955372"</string>
		</dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			KU9yCQH0VdOpj2CwFaVbdVBqpYU=
			</data>
			<key>requirement</key>
			<string>cdhash H"294f720901f455d3a98f60b015a55b75506aa585"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/customtkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter</string>
		</dict>
		<key>Frameworks/img</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/img</string>
		</dict>
		<key>Frameworks/keyring-25.6.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/keyring-25.6.0.dist-info</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dPvdj+huQJ122lqIIdlnaZHfiUo=
			</data>
			<key>requirement</key>
			<string>cdhash H"74fbdd8fe86e409d76da5a8821d9676991df894a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			PDz6OvOt+lmtNcU8Qpsd+dPfU3k=
			</data>
			<key>requirement</key>
			<string>cdhash H"3c3cfa3af3adfa59ad35c53c429b1df9d3df5379"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ye3j60zvpUnAz8H2n0e4C8WCf/E=
			</data>
			<key>requirement</key>
			<string>cdhash H"61ede3eb4cefa549c0cfc1f69f47b80bc5827ff1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			tvaWfdpRB1l3lriWgzU+pGFGSZg=
			</data>
			<key>requirement</key>
			<string>cdhash H"b6f6967dda5107597796b89683353ea461464998"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XPJ2128SQRDe9zOp+RxE6oBh2k8=
			</data>
			<key>requirement</key>
			<string>cdhash H"5cf276d76f124110def733a9f91c44ea8061da4f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/L42fCyix4BZZcNGZLcjtar8Z3o=
			</data>
			<key>requirement</key>
			<string>cdhash H"fcbe367c2ca2c7805965c34664b723b5aafc677a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			u5PCpv7CIu4WGN/LQWCSRSAhtVQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"bb93c2a6fec222ee1618dfcb416092452021b554"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			x9a4CkRBqGlUMQfLYeSbjr6+m3Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7d6b80a4441a869543107cb61e49b8ebebe9b74"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BcunTTehgDXlxwBK+jFjVnNUukQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"05cba74d37a18035e5c7004afa3163567354ba44"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dFieab/fOS8Cnh9nKHoZBkrpD8c=
			</data>
			<key>requirement</key>
			<string>cdhash H"74589e69bfdf392f029e1f67287a19064ae90fc7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ehY4h98NVoTEEosf7UQoQgT8oLk=
			</data>
			<key>requirement</key>
			<string>cdhash H"7a163887df0d5684c4128b1fed44284204fca0b9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kd5N9+z5CrYUxp2mcpI69E86SU4=
			</data>
			<key>requirement</key>
			<string>cdhash H"91de4df7ecf90ab614c69da672923af44f3a494e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qtoeX2coy8sbBKtb2kF6QkcWmRE=
			</data>
			<key>requirement</key>
			<string>cdhash H"aada1e5f6728cbcb1b04ab5bda417a4247169911"</string>
		</dict>
		<key>Frameworks/lib-dynload/_curses.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			y31gDyokxyaTZKCypslH8TkqATc=
			</data>
			<key>requirement</key>
			<string>cdhash H"cb7d600f2a24c7269364a0b2a6c947f1392a0137"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gV1yFyFDz99qsro7/HwPd20Y2BQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"815d72172143cfdf6ab2ba3bfc7c0f776d18d814"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KxjsiO2sCAK8vRFgsJWf0iSBBHk=
			</data>
			<key>requirement</key>
			<string>cdhash H"2b18ec88edac0802bcbd1160b0959fd224810479"</string>
		</dict>
		<key>Frameworks/lib-dynload/_elementtree.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JTfxymOL2iGqDryyV/gPVyUJHC0=
			</data>
			<key>requirement</key>
			<string>cdhash H"2537f1ca638bda21aa0ebcb257f80f5725091c2d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YivcIfvY+PVkm1ovY5SJNAFOqbI=
			</data>
			<key>requirement</key>
			<string>cdhash H"622bdc21fbd8f8f5649b5a2f63948934014ea9b2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			js6fLrK1uockA4Rst2cpXlXOiFQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"8ece9f2eb2b5ba872403846cb767295e55ce8854"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pB+rEdfHG2b93WVB1tpe+z1o6a8=
			</data>
			<key>requirement</key>
			<string>cdhash H"a41fab11d7c71b66fddd6541d6da5efb3d68e9af"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qFgPEE0frurvCrJ5vifq6cdptgo=
			</data>
			<key>requirement</key>
			<string>cdhash H"a8580f104d1faeeaef0ab279be27eae9c769b60a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9hI68kEj3ACKj/QMqLB0ary6wWk=
			</data>
			<key>requirement</key>
			<string>cdhash H"f6123af24123dc008a8ff40ca8b0746abcbac169"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Fj6WBsTt5ZZeBizl5Z70B0XeREY=
			</data>
			<key>requirement</key>
			<string>cdhash H"163e9606c4ede5965e062ce5e59ef40745de4446"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			n7QbJGiZwKXTKpNDloFtGk7aUdk=
			</data>
			<key>requirement</key>
			<string>cdhash H"9fb41b246899c0a5d32a934396816d1a4eda51d9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BPLhneHDs9Rcijmp7w7e/pWUBRo=
			</data>
			<key>requirement</key>
			<string>cdhash H"04f2e19de1c3b3d45c8a39a9ef0edefe9594051a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VB4lfwsoe9LN4Z7h5WRHIUYlBBg=
			</data>
			<key>requirement</key>
			<string>cdhash H"541e257f0b287bd2cde19ee1e564472146250418"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			duRHBWI4boQRIl6z81CqEw7MR2Y=
			</data>
			<key>requirement</key>
			<string>cdhash H"76e4470562386e8411225eb3f350aa130ecc4766"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			04BIOPzi5rf+btDgh9hmylafASc=
			</data>
			<key>requirement</key>
			<string>cdhash H"d3804838fce2e6b7fe6ed0e087d866ca569f0127"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/2sXOe7VPZobzGAil5QJVbKDGAM=
			</data>
			<key>requirement</key>
			<string>cdhash H"ff6b1739eed53d9a1bcc602297940955b2831803"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XBarQly1oM1AlJ2a08ltYwqlJtI=
			</data>
			<key>requirement</key>
			<string>cdhash H"5c16ab425cb5a0cd40949d9ad3c96d630aa526d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/G8Sf3rZwiohzN/5Yv2Ij9jBfYo=
			</data>
			<key>requirement</key>
			<string>cdhash H"fc6f127f7ad9c22a21ccdff962fd888fd8c17d8a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+6KCTq9HdoVre9fdDBxwa6yM0eE=
			</data>
			<key>requirement</key>
			<string>cdhash H"fba2824eaf4776856b7bd7dd0c1c706bac8cd1e1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hx6oGM32lUF6hGj/f3DWksGUvss=
			</data>
			<key>requirement</key>
			<string>cdhash H"871ea818cdf695417a8468ff7f70d692c194becb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fZTpfxmJY/rmg4dr65t28l30EQA=
			</data>
			<key>requirement</key>
			<string>cdhash H"7d94e97f198963fae683876beb9b76f25df41100"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			np+J5KrDjDYGw+j3D6hvLAsa3PE=
			</data>
			<key>requirement</key>
			<string>cdhash H"9e9f89e4aac38c3606c3e8f70fa86f2c0b1adcf1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JAW6052QIk8+4fj75eu0e9V41tk=
			</data>
			<key>requirement</key>
			<string>cdhash H"2405bad39d90224f3ee1f8fbe5ebb47bd578d6d9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wHWv9vhnqWlFzBSpO6xKTmzNqtY=
			</data>
			<key>requirement</key>
			<string>cdhash H"c075aff6f867a96945cc14a93bac4a4e6ccdaad6"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XNZABR2okVDp5QX1hLpDFQvdQdg=
			</data>
			<key>requirement</key>
			<string>cdhash H"5cd640051da89150e9e505f584ba43150bdd41d8"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yhUdALTZ4ySBCMYHt/RoEJF/S1Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"ca151d00b4d9e3248108c607b7f46810917f4b54"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VoU6iLidbrpM4tA296FoEq1jlFs=
			</data>
			<key>requirement</key>
			<string>cdhash H"56853a88b89d6eba4ce2d036f7a16812ad63945b"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DRc2jMRIyInisysLiUHsvaOK+ZI=
			</data>
			<key>requirement</key>
			<string>cdhash H"0d17368cc448c889e2b32b0b8941ecbda38af992"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NEYanediLdPfReWW2qnB4whP3+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"34461a9de7622dd3df45e596daa9c1e3084fdfe0"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KxSq2B3jrP7m3gmuhRcEx27QDKA=
			</data>
			<key>requirement</key>
			<string>cdhash H"2b14aad81de3acfee6de09ae851704c76ed00ca0"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			8xekT95zpuNXkTjUx+zG8DoTSZc=
			</data>
			<key>requirement</key>
			<string>cdhash H"f317a44fde73a6e3579138d4c7ecc6f03a134997"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			citon84B4Cc+LGDQnsUVE5TJkQw=
			</data>
			<key>requirement</key>
			<string>cdhash H"722b689fce01e0273e2c60d09ec5151394c9910c"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			uOa/u6v/oPjwYbGX2nMfhXuVWTc=
			</data>
			<key>requirement</key>
			<string>cdhash H"b8e6bfbbabffa0f8f061b197da731f857b955937"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LHwHtIaBroCb/Kj8zv7xqn2bjmo=
			</data>
			<key>requirement</key>
			<string>cdhash H"2c7c07b48681ae809bfca8fccefef1aa7d9b8e6a"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kM0jeMnWRo/tFzw0RRE5PG4ZJ0I=
			</data>
			<key>requirement</key>
			<string>cdhash H"90cd2378c9d6468fed173c344511393c6e192742"</string>
		</dict>
		<key>Frameworks/lib-dynload/syslog.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			L19heRO0J+zfkGS81L4mhcd9Prk=
			</data>
			<key>requirement</key>
			<string>cdhash H"2f5f617913b427ecdf9064bcd4be2685c77d3eb9"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VFMf+Jhgd9tBpZGkRI40VDYbczI=
			</data>
			<key>requirement</key>
			<string>cdhash H"54531ff8986077db41a591a4448e3454361b7332"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			iOH1fFcAPQPQzxWnIGFxmTQhj6s=
			</data>
			<key>requirement</key>
			<string>cdhash H"88e1f57c57003d03d0cf15a72061719934218fab"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ta74rb+8jl3/Ha2dXICBE/12cUE=
			</data>
			<key>requirement</key>
			<string>cdhash H"b5aef8adbfbc8e5dff1dad9d5c808113fd767141"</string>
		</dict>
		<key>Frameworks/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			gXZExuzxSZI1sB87ruYCA0WK20M=
			</data>
			<key>requirement</key>
			<string>cdhash H"817644c6ecf1499235b01f3baee60203458adb43"</string>
		</dict>
		<key>Frameworks/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libmpdec.4.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			RwaVX+G0H+4iHj3c8PrinptzJJI=
			</data>
			<key>requirement</key>
			<string>cdhash H"4706955fe1b41fee221e3ddcf0fae29e9b732492"</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ebcy0BHxqln7Ny0+mWcdZGBUH3A=
			</data>
			<key>requirement</key>
			<string>cdhash H"11b732d011f1aa59fb372d3e99671d6460541f70"</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Frameworks/password_dialog.py</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/password_dialog.py</string>
		</dict>
		<key>Frameworks/setuptools</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/setuptools</string>
		</dict>
		<key>Frameworks/xml</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/xml</string>
		</dict>
		<key>Resources/PIL</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/PIL</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			AdCF0RagJK4SaTaLV1Da70ePdKquXFy4YnzpGD/ZeR8=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/img/123.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			zRbt2eaNE2k5bwMRPx6qaxe5d8M6NSKc7Qjkbrb/ODY=
			</data>
		</dict>
		<key>Resources/img/bluetooth.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			AXDfQ7PFxvX7gGQYsojZ3L4PMUU0IIMf0Vbora632Mc=
			</data>
		</dict>
		<key>Resources/img/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			/wu7jVkhEKGPoKTYum8zEAUTPQuFfFMXP41QTSed88g=
			</data>
		</dict>
		<key>Resources/img/erase.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7l2sY7T3qXSjQZXIEDtzavqbz2NSrlH4itxtn6/igJc=
			</data>
		</dict>
		<key>Resources/img/exit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b5d6vlIoz/sJxS4j+nsUsX46dyNReM1kJkVz+QSIJJ8=
			</data>
		</dict>
		<key>Resources/img/findmy.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			6O5TLJgtkHq7LHYtkdu2JJQppDEmUUKnPrSZurSZ3Ew=
			</data>
		</dict>
		<key>Resources/img/shutdown.png</key>
		<dict>
			<key>hash2</key>
			<data>
			adYIbIXC8nbpXDrEmKQUQo+zPZY5R8nBZJbzWaxWFPc=
			</data>
		</dict>
		<key>Resources/img/sysinfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			vZn9xKNKjSY5I0arOZQ4oRzT4WN8azNInwkwJN8dE2k=
			</data>
		</dict>
		<key>Resources/img/vmt4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Z+9QI12KB/6hF5XKxLEa4cOc8Wnmo3UmrJIfxOJpw4o=
			</data>
		</dict>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash2</key>
			<data>
			rNQktSPlSyE+VKfSgMGXqpOZTyTJd6h4JNlIUpdDoyA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			WqaKLAzi7FB7Ewbroi4ebjBga54GoM94tWkm7qx3p90=
			</data>
		</dict>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			tjT6ORXV56DHmCh07PXANHtyqJKen6F6ymXv2Z5QlMk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vmt2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4tQNe6UJM9x0ErqEsv+KjZc2XFQ862q3TRtoRReJC34=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vmt3.png</key>
		<dict>
			<key>hash2</key>
			<data>
			AYpJ9bqq/THU+Z4qwBBDi3v/yEMKTBa/qkv9YXT9E2g=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GeEl6CBzyDu50Hu4C2j+XLVl/4jQbIC0t3SlJyK7BsA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1ju/nmVj5Szaqn5wjFVQo4KMWcXB/CjOpYGSfMgk4Kk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			sTzAaJlRqYcg0y5qmy5xwuyQ4gRCnPggVcJIdWnX8i4=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			FJBSpYLuVVrMudR2GNQ0yVHsWgz64STZc5AUwJRJOOU=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			He/b6Cc/DD4cLlU4K4nqPm3/r1AILuwNskrylo3JHbc=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3XK2IiySOO3EzgPGPNv7hpDmm7gIKTs1mkCU7SSaGAg=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash2</key>
			<data>
			NpKcDDxjT73l8z1/Nc1X0EFty0BKBNJ7s7LIbSUh3r8=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			Wk6NcDzc61n+S7/O+JSQG4ZkNPLWS8O+iUcR9c4+arw=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CD+ufxpcMTBVE8LJvKzniszXAhS8xNABGEo6MHqbwPU=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2_rounded.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iecaM5vNBiUb+RG27Jy3/GUEgorL3Cd8XVY6gYkRcFc=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2_temp.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2V96COlhVUrjv1CRdLPB+dFuRLsubZcZaJJBfUus4nQ=
			</data>
		</dict>
		<key>Resources/img/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jOZag2Q3yCpPYvFFXgOuK2Baf8a+HUylmWUhBDhBI6Q=
			</data>
		</dict>
		<key>Resources/img/xxx.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
		<key>Resources/img/xxx.png</key>
		<dict>
			<key>hash2</key>
			<data>
			DltqZK/wdHkI4bv8nRiuvrd1BdOrrb3uBTsOzFcZRZ8=
			</data>
		</dict>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			yOfFn7nhq1viUuO2rlX5hyia0vArQ9JV/8opaAICgTA=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			7NPvIdku/VrKm7yv+ITV0DWiNJ8o/9GzDdwNyBPR31c=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			rblFkpNuBhHlkhAnZmha0YXeMvlvn59Z1WHiRV8i9oA=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth.png</key>
		<dict>
			<key>hash2</key>
			<data>
			n+3wBQtqTyxoYteALhMKiZcHUdA1B07ssRXWRE88dDQ=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8IWgML4+zBqL3Is226iqcU6EUotB/sTfNaJ6SfeMef8=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			1V1jeQug46T0y08D/mYq79KveXSvkHV559jV7HKjgF0=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XsQmYdvHwyFU0dtXw2caSJKrbExuSwrxFFwUrvUeCyU=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OEJKmlGH50iAIpFiklwgppToeAxlRc93d70whg30LxA=
			</data>
		</dict>
		<key>Resources/img/xxx/clear.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JMy6eOPBIeGPyt7CJb0POj5Gk/27KHY/7WH+EncDbgo=
			</data>
		</dict>
		<key>Resources/img/xxx/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			EVrTPOkfu3b6zVGgdbFJqeL4YSUjlAaHNBfGUMoNb8w=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			LDnYsUI7Tbe+oZvhEh+36wBZLpo3JsKIUU8tq3vcx1s=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			8s+4K1yGSugGA3D+rfgAcSIaVxl7b1YT+GN+tiwKA1A=
			</data>
		</dict>
		<key>Resources/img/xxx/xxx.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rrsBqmDhsnewSY7OzcdCmv3LFV8XTgh4kDnyoL33s28=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			htoPAa6uRjSKPD1GUZXcHOzN55956HdppkuNoEsqR0E=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			rS2xXSTtNujJISr88RqlBhI4T0Wvx4yWyQLTSGSZ2tM=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			n89rLEVb24kBQYt8Ts1/U/lrTohUbwAd2PIXm06tjJo=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			PZUExdf71Ui/so67QXpySuHtCi3+J3wvF4ORK6k/S8U=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			8ibyc9zH2ST1JDZHWlQZHEUPx9kVaXfVy8z5af/6OUk=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ohh1dke28/NdSNkZ6nkVSwIKkLJTOwIfEwnXKva3pkg=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
		<key>Resources/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libmpdec.4.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libmpdec.4.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libssl.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.3.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Resources/password_dialog.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jKoWHr+PIsGwZpLlIzCb5FJrrX+1xVN485fkgdO4D80=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			anuQ7/7h4J1bSEzfcjIBakPi2cyVQ7y7jklLHsBeH1k=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			DY08buueu+hsrH1ghhVSQzwynanqUSSLYdAr4uXmQDA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			mguMlWGMX+VHnMpKOjjQidIo1ssRlCFu4a4mBpz1s2M=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CO3fD9yylANiXkrMo4qHLV/mqXL2sC5JFKgt1yWAT+A=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			N/7c/79zxOufBY9HZ3yzMgOkNv+TkOTTio4BydrSjgs=
			</data>
		</dict>
		<key>Resources/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			Wk6NcDzc61n+S7/O+JSQG4ZkNPLWS8O+iUcR9c4+arw=
			</data>
		</dict>
		<key>Resources/xml/05-16 test.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			ETMDoHKhN8rUPDvfDwP/rQ/Gq80briAr5oiqYqfGk6s=
			</data>
		</dict>
		<key>Resources/xml/8123456.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			tfu8h4Qn7pR/W6uPxY0CnBth/JI9OUvU4jUF0ITj1SI=
			</data>
		</dict>
		<key>Resources/xml/blancco_report.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			+KJqwa6ljhsgBZnHQrq3DX6weuzUeh3k6dzAvu5ag40=
			</data>
		</dict>
		<key>Resources/xml/old_blancco_report.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			Ysdy6M0Sk1T2K0FVngnngSBC7IIm9+Q88znIGeJtsn4=
			</data>
		</dict>
		<key>Resources/xml/reports (1).xml</key>
		<dict>
			<key>hash2</key>
			<data>
			5cefcTdr8ZTk28szPsu9MUOaQ1m9byeo/ZwzehmJjn8=
			</data>
		</dict>
		<key>Resources/xml/reports.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			vWj484ytl9qRKVB8o0fa8yC9LzefN06omu4twep4iLM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
