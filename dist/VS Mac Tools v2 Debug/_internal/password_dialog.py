import customtkinter as ctk
import os
import keyring
import getpass

class PasswordDialog(ctk.CTkToplevel):
    """Dialog for entering sudo password"""
    
    def __init__(self, parent, title="Enter Password", message="Please enter your sudo password:"):
        super().__init__(parent)
        self.parent = parent
        self.title(title)
        self.geometry("400x200")
        self.resizable(False, False)
        self.grab_set()  # Make dialog modal
        
        # Set dark theme
        self.configure(fg_color="#1E1E1E")
        
        # Create form
        self.message_label = ctk.CTkLabel(self, text=message, font=("SF Pro", 14))
        self.message_label.pack(pady=(20, 10))
        
        # Password field
        self.password_var = ctk.StringVar()
        self.password_entry = ctk.CTkEntry(self, textvariable=self.password_var, show="*", width=300)
        self.password_entry.pack(pady=10)
        
        # Remember password checkbox
        self.remember_var = ctk.BooleanVar(value=False)
        self.remember_checkbox = ctk.CTkCheckBox(self, text="Remember password", variable=self.remember_var)
        self.remember_checkbox.pack(pady=5)
        
        # Buttons frame
        self.button_frame = ctk.CTkFrame(self, fg_color="#1E1E1E")
        self.button_frame.pack(fill="x", pady=10)
        
        # OK button
        self.ok_button = ctk.CTkButton(
            self.button_frame, 
            text="OK", 
            command=self.on_ok,
            fg_color="#007ACC", 
            hover_color="#005999"
        )
        self.ok_button.pack(side="right", padx=10)
        
        # Cancel button
        self.cancel_button = ctk.CTkButton(
            self.button_frame, 
            text="Cancel", 
            command=self.on_cancel,
            fg_color="#333333", 
            hover_color="#444444"
        )
        self.cancel_button.pack(side="right", padx=10)
        
        # Set focus to password field
        self.password_entry.focus_set()
        
        # Bind Enter key to OK button
        self.bind("<Return>", lambda event: self.on_ok())
        
        # Result
        self.result = {"password": None, "remember": False, "cancelled": True}
        
        # Try to load saved password
        self.load_saved_password()
    
    def load_saved_password(self):
        """Try to load saved password from keyring"""
        try:
            username = getpass.getuser()
            service_name = "VS_Mac_Tools"
            saved_password = keyring.get_password(service_name, username)
            if saved_password:
                self.password_var.set(saved_password)
                self.remember_var.set(True)
        except:
            # If keyring fails, just continue without a saved password
            pass
    
    def on_ok(self):
        """Handle OK button click"""
        password = self.password_var.get()
        remember = self.remember_var.get()
        
        if password:
            self.result = {
                "password": password,
                "remember": remember,
                "cancelled": False
            }
            
            # Save password if requested
            if remember:
                try:
                    username = getpass.getuser()
                    service_name = "VS_Mac_Tools"
                    keyring.set_password(service_name, username, password)
                except:
                    # If keyring fails, just continue without saving
                    pass
            
            self.destroy()
    
    def on_cancel(self):
        """Handle Cancel button click"""
        self.result = {"password": None, "remember": False, "cancelled": True}
        self.destroy()

def get_sudo_password(parent, title="Enter Password", message="Please enter your sudo password:"):
    """Show password dialog and return result"""
    dialog = PasswordDialog(parent, title, message)
    dialog.wait_window()
    return dialog.result
