<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/base_library.zip</key>
		<data>
		MSxKOvRnuHU5tebrUWbqU80mjY8=
		</data>
		<key>Resources/icon-windowed.icns</key>
		<data>
		eEHOuYpZLB0vKGVIWGZOh5rH8+o=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			KU9yCQH0VdOpj2CwFaVbdVBqpYU=
			</data>
			<key>requirement</key>
			<string>cdhash H"294f720901f455d3a98f60b015a55b75506aa585"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			PDz6OvOt+lmtNcU8Qpsd+dPfU3k=
			</data>
			<key>requirement</key>
			<string>cdhash H"3c3cfa3af3adfa59ad35c53c429b1df9d3df5379"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ye3j60zvpUnAz8H2n0e4C8WCf/E=
			</data>
			<key>requirement</key>
			<string>cdhash H"61ede3eb4cefa549c0cfc1f69f47b80bc5827ff1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			tvaWfdpRB1l3lriWgzU+pGFGSZg=
			</data>
			<key>requirement</key>
			<string>cdhash H"b6f6967dda5107597796b89683353ea461464998"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XPJ2128SQRDe9zOp+RxE6oBh2k8=
			</data>
			<key>requirement</key>
			<string>cdhash H"5cf276d76f124110def733a9f91c44ea8061da4f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/L42fCyix4BZZcNGZLcjtar8Z3o=
			</data>
			<key>requirement</key>
			<string>cdhash H"fcbe367c2ca2c7805965c34664b723b5aafc677a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			u5PCpv7CIu4WGN/LQWCSRSAhtVQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"bb93c2a6fec222ee1618dfcb416092452021b554"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			x9a4CkRBqGlUMQfLYeSbjr6+m3Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7d6b80a4441a869543107cb61e49b8ebebe9b74"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BcunTTehgDXlxwBK+jFjVnNUukQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"05cba74d37a18035e5c7004afa3163567354ba44"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dFieab/fOS8Cnh9nKHoZBkrpD8c=
			</data>
			<key>requirement</key>
			<string>cdhash H"74589e69bfdf392f029e1f67287a19064ae90fc7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ehY4h98NVoTEEosf7UQoQgT8oLk=
			</data>
			<key>requirement</key>
			<string>cdhash H"7a163887df0d5684c4128b1fed44284204fca0b9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kd5N9+z5CrYUxp2mcpI69E86SU4=
			</data>
			<key>requirement</key>
			<string>cdhash H"91de4df7ecf90ab614c69da672923af44f3a494e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gV1yFyFDz99qsro7/HwPd20Y2BQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"815d72172143cfdf6ab2ba3bfc7c0f776d18d814"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KxjsiO2sCAK8vRFgsJWf0iSBBHk=
			</data>
			<key>requirement</key>
			<string>cdhash H"2b18ec88edac0802bcbd1160b0959fd224810479"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YivcIfvY+PVkm1ovY5SJNAFOqbI=
			</data>
			<key>requirement</key>
			<string>cdhash H"622bdc21fbd8f8f5649b5a2f63948934014ea9b2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			js6fLrK1uockA4Rst2cpXlXOiFQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"8ece9f2eb2b5ba872403846cb767295e55ce8854"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pB+rEdfHG2b93WVB1tpe+z1o6a8=
			</data>
			<key>requirement</key>
			<string>cdhash H"a41fab11d7c71b66fddd6541d6da5efb3d68e9af"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qFgPEE0frurvCrJ5vifq6cdptgo=
			</data>
			<key>requirement</key>
			<string>cdhash H"a8580f104d1faeeaef0ab279be27eae9c769b60a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9hI68kEj3ACKj/QMqLB0ary6wWk=
			</data>
			<key>requirement</key>
			<string>cdhash H"f6123af24123dc008a8ff40ca8b0746abcbac169"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Fj6WBsTt5ZZeBizl5Z70B0XeREY=
			</data>
			<key>requirement</key>
			<string>cdhash H"163e9606c4ede5965e062ce5e59ef40745de4446"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BPLhneHDs9Rcijmp7w7e/pWUBRo=
			</data>
			<key>requirement</key>
			<string>cdhash H"04f2e19de1c3b3d45c8a39a9ef0edefe9594051a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VB4lfwsoe9LN4Z7h5WRHIUYlBBg=
			</data>
			<key>requirement</key>
			<string>cdhash H"541e257f0b287bd2cde19ee1e564472146250418"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			04BIOPzi5rf+btDgh9hmylafASc=
			</data>
			<key>requirement</key>
			<string>cdhash H"d3804838fce2e6b7fe6ed0e087d866ca569f0127"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XBarQly1oM1AlJ2a08ltYwqlJtI=
			</data>
			<key>requirement</key>
			<string>cdhash H"5c16ab425cb5a0cd40949d9ad3c96d630aa526d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+6KCTq9HdoVre9fdDBxwa6yM0eE=
			</data>
			<key>requirement</key>
			<string>cdhash H"fba2824eaf4776856b7bd7dd0c1c706bac8cd1e1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hx6oGM32lUF6hGj/f3DWksGUvss=
			</data>
			<key>requirement</key>
			<string>cdhash H"871ea818cdf695417a8468ff7f70d692c194becb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fZTpfxmJY/rmg4dr65t28l30EQA=
			</data>
			<key>requirement</key>
			<string>cdhash H"7d94e97f198963fae683876beb9b76f25df41100"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			np+J5KrDjDYGw+j3D6hvLAsa3PE=
			</data>
			<key>requirement</key>
			<string>cdhash H"9e9f89e4aac38c3606c3e8f70fa86f2c0b1adcf1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wHWv9vhnqWlFzBSpO6xKTmzNqtY=
			</data>
			<key>requirement</key>
			<string>cdhash H"c075aff6f867a96945cc14a93bac4a4e6ccdaad6"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XNZABR2okVDp5QX1hLpDFQvdQdg=
			</data>
			<key>requirement</key>
			<string>cdhash H"5cd640051da89150e9e505f584ba43150bdd41d8"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yhUdALTZ4ySBCMYHt/RoEJF/S1Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"ca151d00b4d9e3248108c607b7f46810917f4b54"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VoU6iLidbrpM4tA296FoEq1jlFs=
			</data>
			<key>requirement</key>
			<string>cdhash H"56853a88b89d6eba4ce2d036f7a16812ad63945b"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DRc2jMRIyInisysLiUHsvaOK+ZI=
			</data>
			<key>requirement</key>
			<string>cdhash H"0d17368cc448c889e2b32b0b8941ecbda38af992"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NEYanediLdPfReWW2qnB4whP3+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"34461a9de7622dd3df45e596daa9c1e3084fdfe0"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KxSq2B3jrP7m3gmuhRcEx27QDKA=
			</data>
			<key>requirement</key>
			<string>cdhash H"2b14aad81de3acfee6de09ae851704c76ed00ca0"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LHwHtIaBroCb/Kj8zv7xqn2bjmo=
			</data>
			<key>requirement</key>
			<string>cdhash H"2c7c07b48681ae809bfca8fccefef1aa7d9b8e6a"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kM0jeMnWRo/tFzw0RRE5PG4ZJ0I=
			</data>
			<key>requirement</key>
			<string>cdhash H"90cd2378c9d6468fed173c344511393c6e192742"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			iOH1fFcAPQPQzxWnIGFxmTQhj6s=
			</data>
			<key>requirement</key>
			<string>cdhash H"88e1f57c57003d03d0cf15a72061719934218fab"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ta74rb+8jl3/Ha2dXICBE/12cUE=
			</data>
			<key>requirement</key>
			<string>cdhash H"b5aef8adbfbc8e5dff1dad9d5c808113fd767141"</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			gXZExuzxSZI1sB87ruYCA0WK20M=
			</data>
			<key>requirement</key>
			<string>cdhash H"817644c6ecf1499235b01f3baee60203458adb43"</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			/fEuO9L3oJosKcfyyN21D1y5+hs=
			</data>
			<key>requirement</key>
			<string>cdhash H"fdf12e3bd2f7a09a2c29c7f2c8ddb50f5cb9fa1b"</string>
		</dict>
		<key>Frameworks/libmpdec.4.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			RwaVX+G0H+4iHj3c8PrinptzJJI=
			</data>
			<key>requirement</key>
			<string>cdhash H"4706955fe1b41fee221e3ddcf0fae29e9b732492"</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			Sq3Grp3K5uKxFMZF5zafXq1hGtZKXvvQLIxSU/jw3pQ=
			</data>
		</dict>
		<key>Resources/icon-windowed.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			uQo7VuWRab4Phv4EEGmfQsyqFqDIXZgO8OtgaAMvCzY=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libmpdec.4.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libmpdec.4.dylib</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
