#!/bin/bash

# VS Mac Tools v2 Launcher with Virtual Environment
# This ensures all dependencies are properly isolated

echo "🚀 Launching VS Mac Tools v2..."

# Set environment to silence deprecation warnings
export TK_SILENCE_DEPRECATION=1

# Change to the script directory
cd "$(dirname "$0")"

# Check if the Python script exists
if [ ! -f "vs_mac_tools_v2.py" ]; then
    echo "❌ Error: vs_mac_tools_v2.py not found in current directory"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    /usr/bin/python3 -m venv venv

    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi

    echo "✅ Virtual environment created"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Check if required packages are installed
echo "🔍 Checking Python packages..."
python -c "import customtkinter, PIL, keyring" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "📦 Installing required packages..."
    echo "This may take a moment..."

    # Install packages in virtual environment
    pip install customtkinter pillow keyring

    if [ $? -ne 0 ]; then
        echo "❌ Failed to install packages"
        exit 1
    fi

    echo "✅ Packages installed successfully"
fi

# Launch the application
echo "🎯 Starting VS Mac Tools v2..."
python vs_mac_tools_v2.py

echo "👋 VS Mac Tools v2 closed"
