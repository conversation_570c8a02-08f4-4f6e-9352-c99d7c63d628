#!/bin/bash

# Simple launcher for VS Mac Tools v2
# This bypasses any virtual environment issues

echo "🚀 Launching VS Mac Tools v2..."

# Set environment to silence deprecation warnings
export TK_SILENCE_DEPRECATION=1

# Change to the script directory
cd "$(dirname "$0")"

# Check if the Python script exists
if [ ! -f "vs_mac_tools_v2.py" ]; then
    echo "❌ Error: vs_mac_tools_v2.py not found in current directory"
    exit 1
fi

# Check if system Python 3 exists
if [ ! -f "/usr/bin/python3" ]; then
    echo "❌ Error: System Python 3 not found"
    echo "Please install Python 3 from https://www.python.org/"
    exit 1
fi

# Check if required packages are installed
echo "🔍 Checking Python packages..."
/usr/bin/python3 -c "import customtkinter, PIL, keyring" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "📦 Installing required packages..."
    echo "This may take a moment..."
    
    # Try to install packages
    /usr/bin/python3 -m pip install --user customtkinter pillow keyring
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install packages automatically"
        echo "Please install manually:"
        echo "  /usr/bin/python3 -m pip install --user customtkinter pillow keyring"
        exit 1
    fi
    
    echo "✅ Packages installed successfully"
fi

# Launch the application
echo "🎯 Starting VS Mac Tools v2..."
/usr/bin/python3 vs_mac_tools_v2.py

echo "👋 VS Mac Tools v2 closed"
