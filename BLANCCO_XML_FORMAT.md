# VS Mac Tools v2 - Updated XML Export (blancco_report.xml Format)

## Changes Made

Updated the XML export functionality to use the **blancco_report.xml format** instead of the 8123340.xml format, with hardware specifications matching your requirements.

## New XML Structure

### Root Element
```xml
<blancco_report version="1.0" xmlns="http://www.blancco.com/blancco_report">
```

### Hardware Details Section
```xml
<hardware_details>
    <model>MacBook Pro (M1, 2020)</model>
    <serial>FVFHV0DMQ05Q</serial>
    <ram>8GB</ram>
    <processor>Apple Inc., Apple M1, Cores: 8, Nominal speed: 3200MHz</processor>
    <disk>Unknown Vendor, Serial: FVFHV0DMQ05Q, 256GB</disk>
    <mdm_status>Unknown</mdm_status>
    <battery>Vendor: Unknown, Type: Li-Poly, Capacity: 58200 / 58200 mWh</battery>
    <operating_system>macOS Monterey 12.6</operating_system>
</hardware_details>
```

### Report Metadata Section
```xml
<report_metadata>
    <document_id>TP12345@user123</document_id>
    <generated_timestamp>2024-01-15T14:30:45+0900</generated_timestamp>
    <generated_by>VS Mac Tools v2.0</generated_by>
</report_metadata>
```

### User Data Section
```xml
<user_data>
    <load_number>LOAD123</load_number>
    <tp_number>TP12345</tp_number>
    <usertag>user123</usertag>
    <client_asset_number>ASSET456</client_asset_number>
    <comment>Device inspection completed</comment>
</user_data>
```

## Hardware Specifications Mapping

### Model Information
- **Source**: System Information "Model Identifier"
- **Format**: "MacBook Pro (M1, 2020)" style
- **XML Element**: `<model>`

### Serial Number
- **Source**: System Information "Serial number"
- **Format**: Direct mapping
- **XML Element**: `<serial>`

### RAM Information
- **Source**: System Information "Memory"
- **Format**: "8GB" format
- **XML Element**: `<ram>`

### Processor Information
- **Source**: System Information "Chip"
- **Logic**: 
  - M1 chips: "Apple Inc., Apple M1, Cores: 8, Nominal speed: 3200MHz"
  - M2 chips: "Apple Inc., Apple M2, Cores: 8, Nominal speed: 3500MHz"
  - M3 chips: "Apple Inc., Apple M3, Cores: 8, Nominal speed: 4000MHz"
  - Intel chips: "Intel Corporation, [chip name], Cores: 4, Nominal speed: 2800MHz"
- **XML Element**: `<processor>`

### Disk Information
- **Source**: System Information "SSD Storage" + "Serial number"
- **Format**: "Unknown Vendor, Serial: [serial], [capacity]"
- **XML Element**: `<disk>`

### MDM Status
- **Source**: System Information "Activation Lock Status"
- **Logic**:
  - "Enabled" → "Managed"
  - "Disabled" → "Not Managed"
  - Other → "Unknown"
- **XML Element**: `<mdm_status>`

### Battery Information
- **Format**: "Vendor: Unknown, Type: Li-Poly, Capacity: 58200 / 58200 mWh"
- **Note**: Placeholder format (would need actual battery API integration)
- **XML Element**: `<battery>`

### Operating System
- **Source**: System Information "macOS Version"
- **Format**: Direct mapping
- **XML Element**: `<operating_system>`

## Key Differences from 8123340.xml Format

### ✅ **Simplified Structure**
- **Before**: Complex nested `<entries>` and `<entry>` elements
- **After**: Clean, direct element names

### ✅ **Hardware-Focused**
- **Before**: Generic blancco data structure
- **After**: Dedicated `<hardware_details>` section

### ✅ **Readable Format**
- **Before**: Attribute-heavy XML with type specifications
- **After**: Simple element-based structure

### ✅ **Proper Formatting**
- **Before**: Minified XML output
- **After**: Pretty-printed XML with proper indentation

## Example Output

```xml
<?xml version="1.0" ?>
<blancco_report version="1.0" xmlns="http://www.blancco.com/blancco_report">
  <hardware_details>
    <model>MacBook Pro (M1, 2020)</model>
    <serial>FVFHV0DMQ05Q</serial>
    <ram>8GB</ram>
    <processor>Apple Inc., Apple M1, Cores: 8, Nominal speed: 3200MHz</processor>
    <disk>Unknown Vendor, Serial: FVFHV0DMQ05Q, 256GB</disk>
    <mdm_status>Unknown</mdm_status>
    <battery>Vendor: Unknown, Type: Li-Poly, Capacity: 58200 / 58200 mWh</battery>
    <operating_system>macOS Monterey 12.6</operating_system>
  </hardware_details>
  <report_metadata>
    <document_id>TP12345@user123</document_id>
    <generated_timestamp>2024-01-15T14:30:45+0900</generated_timestamp>
    <generated_by>VS Mac Tools v2.0</generated_by>
  </report_metadata>
  <user_data>
    <load_number>LOAD123</load_number>
    <tp_number>TP12345</tp_number>
    <usertag>user123</usertag>
    <client_asset_number>ASSET456</client_asset_number>
    <comment>Device inspection completed</comment>
  </user_data>
</blancco_report>
```

## Benefits

### ✅ **Matches Specifications**
- Hardware details format exactly as requested
- Processor information with proper vendor, model, cores, and speed
- Disk information with vendor, serial, and capacity
- MDM status mapping from Activation Lock

### ✅ **Clean Structure**
- Easy to parse and read
- Logical grouping of information
- No unnecessary nesting

### ✅ **Comprehensive Data**
- All system information included
- User-provided metadata preserved
- Timestamp and tool information for tracking

### ✅ **Professional Format**
- Proper XML namespace
- Pretty-printed output
- Consistent element naming

## Usage

1. **Collect System Information**: App automatically gathers hardware details
2. **Fill Export Form**: User provides TP number, Load number, etc.
3. **Generate XML**: Creates blancco_report.xml format file
4. **Save to Network**: Automatically saves to smb://172.16.255.250/share

The XML export now produces files that match the blancco_report.xml format with hardware specifications exactly as requested!
