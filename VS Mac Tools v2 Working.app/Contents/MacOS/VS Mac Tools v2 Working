#!/bin/bash

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
PYTHON_SCRIPT_DIR="/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker"

# Set environment
export TK_SILENCE_DEPRECATION=1

# Change to the Python script directory
cd "$PYTHON_SCRIPT_DIR"

# Check if Python script exists
if [ ! -f "vs_mac_tools_v2.py" ]; then
    osascript -e 'display alert "Error" message "VS Mac Tools v2 script not found at: '"$PYTHON_SCRIPT_DIR"'"'
    exit 1
fi

# Check if Python 3 is available
if [ ! -f "/usr/bin/python3" ]; then
    osascript -e 'display alert "Python Required" message "Python 3 is required but not found. Please install Python 3."'
    exit 1
fi

# Check if required modules are available
/usr/bin/python3 -c "import customtkinter, PIL, keyring" 2>/dev/null
if [ $? -ne 0 ]; then
    # Show dialog asking to install dependencies
    result=$(osascript -e 'display dialog "Required Python packages are missing. Install them now?" buttons {"Cancel", "Install"} default button "Install"' 2>/dev/null)
    
    if [[ $result == *"Install"* ]]; then
        # Try to install packages
        osascript -e 'display notification "Installing Python packages..." with title "VS Mac Tools v2"'
        
        # Install packages
        /usr/bin/python3 -m pip install --user customtkinter pillow keyring 2>/dev/null
        
        if [ $? -eq 0 ]; then
            osascript -e 'display notification "Packages installed successfully!" with title "VS Mac Tools v2"'
        else
            osascript -e 'display alert "Installation Failed" message "Could not install required packages. Please install manually: pip3 install customtkinter pillow keyring"'
            exit 1
        fi
    else
        exit 1
    fi
fi

# Launch the Python app
echo "Launching VS Mac Tools v2..."
/usr/bin/python3 vs_mac_tools_v2.py

# Check if it exited with an error (but ignore normal exit and KeyboardInterrupt)
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ] && [ $EXIT_CODE -ne 130 ] && [ $EXIT_CODE -ne 2 ]; then
    osascript -e 'display alert "Application Error" message "VS Mac Tools v2 exited with an error. Check the terminal for details."'
fi
