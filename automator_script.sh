#!/bin/bash

# Automator shell script to run VS Mac Tools v2
# This script will be used in an Automator "Run Shell Script" action

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the application directory
cd "$SCRIPT_DIR"

# Check if virtual environment exists, if not create it
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install customtkinter pillow keyring darkdetect
else
    # Activate the virtual environment
    source venv/bin/activate
fi

# Run the Python application
python3 vs_mac_tools_v2.py

# Keep the script running until the Python app closes
wait
