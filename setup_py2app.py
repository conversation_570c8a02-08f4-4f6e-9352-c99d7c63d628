"""
py2app setup script for VS Mac Tools v2
"""

from setuptools import setup
import os

# Get the directory of this script
APP_DIR = os.path.dirname(os.path.abspath(__file__))

APP = ['vs_mac_tools_v2.py']
DATA_FILES = [
    ('img', ['img/bluetooth.icns', 'img/findmy.icns', 'img/device.icns', 
             'img/sysinfo.icns', 'img/shutdown.png', 'img/erase.png', 
             'img/exit.png', 'img/vsmactool.png']),
    ('xml', ['xml/blancco_report.xml']),
    ('', ['password_dialog.py'])
]

OPTIONS = {
    'argv_emulation': False,
    'iconfile': 'img/vsmactool.icns',
    'plist': {
        'CFBundleName': 'VS Mac Tools v2',
        'CFBundleDisplayName': 'VS Mac Tools v2',
        'CFBundleIdentifier': 'com.vonzki.vsmactools',
        'CFBundleVersion': '2.0.1',
        'CFBundleShortVersionString': '2.0.1',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.15.0',
    },
    'packages': ['customtkinter', 'PIL', 'keyring'],
    'includes': ['tkinter', 'tkinter.filedialog', 'tkinter.messagebox'],
    'excludes': ['numpy', 'matplotlib', 'scipy'],
    'resources': ['img', 'xml'],
    'optimize': 0,
}

setup(
    app=APP,
    data_files=DATA_FILES,
    options={'py2app': OPTIONS},
    setup_requires=['py2app'],
    name='VS Mac Tools v2',
    version='2.0.1',
)
