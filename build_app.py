#!/usr/bin/env python3
"""
Build script for VS Mac Tools v2 - Creates macOS .app bundle using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'pyinstaller',
        'customtkinter',
        'pillow',
        'keyring'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\nInstalling missing packages: {', '.join(missing_packages)}")
        for package in missing_packages:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
        print("✅ All dependencies installed")
    
    return True

def clean_build():
    """Clean previous build artifacts"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 Cleaned {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"🧹 Cleaned {file}")

def create_app_icon():
    """Create app icon from vs_ade.png"""
    icon_source = "img/vs_ade.png"
    icon_output = "img/app_icon.icns"
    
    if os.path.exists(icon_source):
        try:
            # Use sips to convert PNG to ICNS (macOS built-in tool)
            subprocess.run([
                'sips', '-s', 'format', 'icns', 
                icon_source, '--out', icon_output
            ], check=True, capture_output=True)
            print(f"✅ Created app icon: {icon_output}")
            return icon_output
        except subprocess.CalledProcessError:
            print(f"⚠️  Could not create ICNS from {icon_source}, using default")
            return None
    else:
        print(f"⚠️  Icon source {icon_source} not found, using default")
        return None

def build_app():
    """Build the macOS app using PyInstaller"""
    
    print("🚀 Starting VS Mac Tools v2 build process...")
    
    # Check dependencies
    check_dependencies()
    
    # Clean previous builds
    clean_build()
    
    # Create app icon
    app_icon = create_app_icon()
    
    # PyInstaller command with comprehensive options
    cmd = [
        'pyinstaller',
        '--onedir',  # Create a one-folder bundle
        '--windowed',  # Don't show console window
        '--name=VS Mac Tools v2',
        '--clean',
        '--noconfirm',
        
        # Add data files
        '--add-data=img:img',
        '--add-data=xml:xml',
        '--add-data=password_dialog.py:.',
        
        # Hidden imports for dependencies that might not be auto-detected
        '--hidden-import=customtkinter',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL._tkinter_finder',
        '--hidden-import=keyring',
        '--hidden-import=keyring.backends',
        '--hidden-import=keyring.backends.macOS',
        '--hidden-import=xml.etree.ElementTree',
        '--hidden-import=xml.dom.minidom',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=queue',
        '--hidden-import=threading',
        '--hidden-import=subprocess',
        '--hidden-import=socket',
        '--hidden-import=csv',
        '--hidden-import=datetime',
        '--hidden-import=pathlib',
        '--hidden-import=platform',
        '--hidden-import=getpass',
        
        # Collect all submodules for problematic packages
        '--collect-submodules=customtkinter',
        '--collect-submodules=PIL',
        '--collect-submodules=keyring',
        
        # Copy metadata for packages
        '--copy-metadata=customtkinter',
        '--copy-metadata=pillow',
        '--copy-metadata=keyring',
        
        # macOS specific options
        '--osx-bundle-identifier=com.vonzki.vsmactools',
        
        # Exclude unnecessary modules to reduce size
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        '--exclude-module=jupyter',
        '--exclude-module=IPython',
        
        # Main script
        'vs_mac_tools_v2.py'
    ]
    
    # Add icon if available
    if app_icon and os.path.exists(app_icon):
        cmd.extend(['--icon', app_icon])
    
    print("🔨 Running PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PyInstaller completed successfully")
        
        # Check if app was created
        app_path = "dist/VS Mac Tools v2.app"
        if os.path.exists(app_path):
            print(f"✅ App created successfully: {app_path}")
            
            # Get app size
            size = get_folder_size(app_path)
            print(f"📦 App size: {size:.1f} MB")
            
            # Set executable permissions
            executable_path = f"{app_path}/Contents/MacOS/VS Mac Tools v2"
            if os.path.exists(executable_path):
                os.chmod(executable_path, 0o755)
                print("✅ Set executable permissions")
            
            return True
        else:
            print("❌ App was not created")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller failed: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False

def get_folder_size(folder_path):
    """Get the size of a folder in MB"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)  # Convert to MB

def test_app():
    """Test the built app"""
    app_path = "dist/VS Mac Tools v2.app"
    if os.path.exists(app_path):
        print("🧪 Testing app...")
        try:
            # Try to open the app
            subprocess.run(['open', app_path], check=True)
            print("✅ App launched successfully")
            print("⚠️  Please test the app manually and close it")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to launch app: {e}")
            return False
    else:
        print("❌ App not found for testing")
        return False

def main():
    """Main build process"""
    print("=" * 60)
    print("VS Mac Tools v2 - macOS App Builder")
    print("=" * 60)
    
    # Check if we're on macOS
    if sys.platform != 'darwin':
        print("❌ This script must be run on macOS")
        sys.exit(1)
    
    # Check if main script exists
    if not os.path.exists('vs_mac_tools_v2.py'):
        print("❌ vs_mac_tools_v2.py not found")
        sys.exit(1)
    
    # Check if img folder exists
    if not os.path.exists('img'):
        print("❌ img folder not found")
        sys.exit(1)
    
    # Build the app
    if build_app():
        print("\n" + "=" * 60)
        print("✅ BUILD SUCCESSFUL!")
        print("=" * 60)
        print(f"📱 App location: dist/VS Mac Tools v2.app")
        print("🚀 You can now distribute this app")
        print("\n💡 To test the app:")
        print("   open 'dist/VS Mac Tools v2.app'")
        print("\n💡 To create a DMG:")
        print("   hdiutil create -volname 'VS Mac Tools v2' -srcfolder 'dist/VS Mac Tools v2.app' -ov -format UDZO 'VS Mac Tools v2.dmg'")
        
        # Ask if user wants to test
        response = input("\n🧪 Do you want to test the app now? (y/n): ")
        if response.lower() in ['y', 'yes']:
            test_app()
    else:
        print("\n" + "=" * 60)
        print("❌ BUILD FAILED!")
        print("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    main()
