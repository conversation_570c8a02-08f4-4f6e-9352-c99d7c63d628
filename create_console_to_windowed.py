#!/usr/bin/env python3
"""
Convert the working console PyInstaller app to a proper windowed app
Since the console version works perfectly, we'll use that as the base
"""

import os
import shutil
import subprocess
import plistlib

def convert_console_to_windowed():
    """Convert console app to windowed app"""
    
    console_app = "dist/Minimal Test Console/Minimal Test Console"
    windowed_app = "VS Mac Tools v2 Console Fixed.app"
    
    print("🔧 Converting console app to windowed app...")
    
    # Check if console app exists
    if not os.path.exists(console_app):
        print("❌ Console app not found. Building it first...")
        
        # Build console version
        result = subprocess.run([
            "/usr/bin/python3", "-m", "PyInstaller",
            "--onedir", "--console",
            "--name=VS Mac Tools Console",
            "--clean", "--noconfirm",
            "--add-data=img:img",
            "--add-data=xml:xml", 
            "--add-data=password_dialog.py:.",
            "--hidden-import=customtkinter",
            "--collect-data=customtkinter",
            "vs_mac_tools_v2.py"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to build console app: {result.stderr}")
            return None
        
        console_app = "dist/VS Mac Tools Console/VS Mac Tools Console"
    
    # Create app bundle structure
    if os.path.exists(windowed_app):
        shutil.rmtree(windowed_app)
    
    contents_dir = f"{windowed_app}/Contents"
    macos_dir = f"{contents_dir}/MacOS"
    resources_dir = f"{contents_dir}/Resources"
    
    os.makedirs(macos_dir, exist_ok=True)
    os.makedirs(resources_dir, exist_ok=True)
    
    print("📁 Created app bundle structure")
    
    # Copy the working console executable
    console_dir = os.path.dirname(console_app)
    shutil.copytree(console_dir, f"{macos_dir}/app_bundle")
    
    print("📦 Copied console app bundle")
    
    # Create wrapper script that runs the console app in background
    wrapper_script = f"""#!/bin/bash
# VS Mac Tools v2 Console Wrapper

# Get the directory of this script
DIR="$( cd "$( dirname "${{BASH_SOURCE[0]}}" )" && pwd )"
APP_BUNDLE="$DIR/app_bundle"

# Set environment
export TK_SILENCE_DEPRECATION=1

# Change to app bundle directory
cd "$APP_BUNDLE"

# Run the console app in background (no terminal window)
exec ./"{os.path.basename(console_app)}" > /dev/null 2>&1
"""
    
    wrapper_path = f"{macos_dir}/VS Mac Tools v2 Console Fixed"
    with open(wrapper_path, "w") as f:
        f.write(wrapper_script)
    
    os.chmod(wrapper_path, 0o755)
    
    print("🚀 Created wrapper script")
    
    # Create Info.plist
    info_plist = {
        'CFBundleExecutable': 'VS Mac Tools v2 Console Fixed',
        'CFBundleIdentifier': 'com.vonzki.vsmactools',
        'CFBundleName': 'VS Mac Tools v2',
        'CFBundleDisplayName': 'VS Mac Tools v2',
        'CFBundleVersion': '2.0.1',
        'CFBundleShortVersionString': '2.0.1',
        'CFBundlePackageType': 'APPL',
        'CFBundleSignature': '????',
        'CFBundleIconFile': 'vsmactool.icns',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.15.0',
        'LSUIElement': False,  # Show in Dock
        'NSAppleScriptEnabled': True
    }
    
    with open(f"{contents_dir}/Info.plist", "wb") as f:
        plistlib.dump(info_plist, f)
    
    print("📄 Created Info.plist")
    
    # Copy icon
    if os.path.exists("img/vsmactool.icns"):
        shutil.copy2("img/vsmactool.icns", f"{resources_dir}/vsmactool.icns")
        print("🎨 Copied app icon")
    
    print(f"✅ Created windowed app: {windowed_app}")
    return windowed_app

def create_truly_standalone():
    """Create a truly standalone version using the working console approach"""
    
    print("🚀 Creating truly standalone VS Mac Tools v2...")
    
    # Build with all dependencies included
    result = subprocess.run([
        "/usr/bin/python3", "-m", "PyInstaller",
        "--onefile", "--windowed",
        "--name=VS Mac Tools v2 Truly Standalone",
        "--clean", "--noconfirm",
        "--icon=img/vsmactool.icns",
        "--add-data=img:img",
        "--add-data=xml:xml",
        "--add-data=password_dialog.py:.",
        "--hidden-import=customtkinter",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.filedialog", 
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=PIL",
        "--hidden-import=keyring",
        "--collect-data=customtkinter",
        "--collect-data=tkinter",
        "--osx-bundle-identifier=com.vonzki.vsmactools",
        "--target-arch=x86_64",  # Use x86_64 for better compatibility
        "vs_mac_tools_v2.py"
    ], capture_output=True, text=True, env={**os.environ, 'TK_SILENCE_DEPRECATION': '1'})
    
    if result.returncode == 0:
        print("✅ Created truly standalone app")
        return "dist/VS Mac Tools v2 Truly Standalone.app"
    else:
        print(f"❌ Failed to create standalone app: {result.stderr}")
        return None

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print("🎯 Creating standalone VS Mac Tools v2 solutions...")
    print("=" * 60)
    
    # Method 1: Convert console to windowed
    print("\n📱 Method 1: Console to Windowed Conversion")
    windowed_app = convert_console_to_windowed()
    
    # Method 2: Truly standalone
    print("\n🔧 Method 2: Truly Standalone Build")
    standalone_app = create_truly_standalone()
    
    print("\n" + "=" * 60)
    print("🎉 RESULTS:")
    
    if windowed_app:
        print(f"✅ Console-based windowed app: {windowed_app}")
    
    if standalone_app:
        print(f"✅ Truly standalone app: {standalone_app}")
    
    print("\n💡 Test both versions to see which works better!")
    print("   The console-based version should definitely work since")
    print("   the console version runs perfectly.")
