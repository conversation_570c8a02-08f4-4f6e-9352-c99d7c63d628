# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for simple test app
"""

import os
import sys

block_cipher = None

a = Analysis(
    ['test_simple_app.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.messagebox',
        '_tkinter',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Simple Test App',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=True,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Simple Test App',
)

app = BUNDLE(
    coll,
    name='Simple Test App.app',
    bundle_identifier='com.test.simpleapp',
    version='1.0',
    info_plist={
        'CFBundleName': 'Simple Test App',
        'CFBundleDisplayName': 'Simple Test App',
        'CFBundleGetInfoString': 'Simple Test App v1.0',
        'CFBundleIdentifier': 'com.test.simpleapp',
        'CFBundleVersion': '1.0',
        'CFBundleShortVersionString': '1.0',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.15',
        'NSRequiresAquaSystemAppearance': False,
        'NSPrincipalClass': 'NSApplication',
        'LSApplicationCategoryType': 'public.app-category.utilities',
    },
)
