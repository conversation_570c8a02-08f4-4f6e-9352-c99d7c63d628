('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS Mac '
 'Tools v2/VS Mac Tools v2',
 False,
 False,
 True,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 'arm64',
 None,
 None,
 '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS Mac '
 'Tools v2/VS Mac Tools v2.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('vs_mac_tools_v2',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/vs_mac_tools_v2.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1749197680,
 [('runw',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/bootloader/Darwin-64bit/runw',
   'EXECUTABLE')],
 '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/Python')
