(['/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/vs_mac_tools_v2.py'],
 ['/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker',
  '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker'],
 ['customtkinter',
  'PIL',
  'PIL._tkinter_finder',
  'PIL.Image',
  'PIL.ImageTk',
  'tkinter',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'tkinter.ttk',
  'xml.dom.minidom',
  'xml.etree.ElementTree',
  'packaging.version',
  'darkdetect',
  'keyring',
  'keyring.backends',
  'jaraco.classes',
  'jaraco.functools',
  'jaraco.context',
  'getpass',
  'platform',
  'socket',
  'subprocess',
  'threading',
  'os',
  'time',
  'csv',
  'datetime',
  'queue',
  'pathlib',
  'password_dialog'],
 [('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib',
  'numpy',
  'scipy',
  'pandas',
  'PyQt5',
  'PyQt6',
  'PySide2',
  'PySide6',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('img/.DS_Store',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/.DS_Store',
   'DATA'),
  ('img/123.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/123.webp',
   'DATA'),
  ('img/bluetooth.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/bluetooth.icns',
   'DATA'),
  ('img/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/device.icns',
   'DATA'),
  ('img/erase.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/erase.png',
   'DATA'),
  ('img/exit.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/exit.png',
   'DATA'),
  ('img/findmy.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/findmy.icns',
   'DATA'),
  ('img/shutdown.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/shutdown.png',
   'DATA'),
  ('img/sysinfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/sysinfo.icns',
   'DATA'),
  ('img/vmt4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vmt4.png',
   'DATA'),
  ('img/vs_icns/A simple and minimalistic image icon featuring the letters "V" '
   'and "S" in a very small size that pertains to a Mac OS system lock '
   'checker. The design should be sleek and modern, emphasizing security and '
   'compatibility with Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/A '
   'simple and minimalistic image icon featuring the letters "V" and "S" in a '
   'very small size that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   'DATA'),
  ('img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   'DATA'),
  ('img/vs_icns/checkADE2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/checkADE2.icns',
   'DATA'),
  ('img/vs_icns/outlook.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/outlook.icns',
   'DATA'),
  ('img/vs_icns/vmt2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vmt2.png',
   'DATA'),
  ('img/vs_icns/vmt3.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vmt3.png',
   'DATA'),
  ('img/vs_icns/vs1.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs1.png',
   'DATA'),
  ('img/vs_icns/vs2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs2.png',
   'DATA'),
  ('img/vs_icns/vs_ade.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade.jpeg',
   'DATA'),
  ('img/vs_icns/vs_ade.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade.webp',
   'DATA'),
  ('img/vs_icns/vs_ade2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade2.png',
   'DATA'),
  ('img/vs_icns/vs_ade4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade4.png',
   'DATA'),
  ('img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vsed '
   'that pertains to a Mac OS system lock checker. The design should be sleek '
   'and modern, emphasizing security and compatibility with Mac OS..png',
   'DATA'),
  ('img/vs_mac_tool_v2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2.icns',
   'DATA'),
  ('img/vs_mac_tool_v2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2.png',
   'DATA'),
  ('img/vs_mac_tool_v2_rounded.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2_rounded.png',
   'DATA'),
  ('img/vsmactool.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vsmactool.png',
   'DATA'),
  ('img/xxx/.DS_Store',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/.DS_Store',
   'DATA'),
  ('img/xxx/GenericAirDiskIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/GenericAirDiskIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarDeleteIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/ToolbarDeleteIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarInfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/ToolbarInfo.icns',
   'DATA'),
  ('img/xxx/bluetooth.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/bluetooth.png',
   'DATA'),
  ('img/xxx/bluetooth2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/bluetooth2.png',
   'DATA'),
  ('img/xxx/checkADE.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE.icns',
   'DATA'),
  ('img/xxx/checkADE.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE.png',
   'DATA'),
  ('img/xxx/checkADE2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE2.png',
   'DATA'),
  ('img/xxx/clear.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/clear.png',
   'DATA'),
  ('img/xxx/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/device.icns',
   'DATA'),
  ('img/xxx/panda.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/panda.icns',
   'DATA'),
  ('img/xxx/panda.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/panda.ico',
   'DATA'),
  ('img/xxx/xxx.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/xxx.png',
   'DATA'),
  ('password_dialog.py',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/password_dialog.py',
   'DATA')],
 '3.12.4 (v3.12.4:8e8a4baf65, Jun  6 2024, 17:33:18) [Clang 13.0.0 '
 '(clang-1300.0.29.30)]',
 [('pyi_rth_setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('vs_mac_tools_v2',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/vs_mac_tools_v2.py',
   'PYSOURCE')],
 [('pkg_resources',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('struct',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/struct.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_aix_support.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_osx_support.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/signals.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/util.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/log.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/selector_events.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/secrets.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/request.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/netrc.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/__init__.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('more_itertools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/more_itertools/__init__.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/more_itertools/recipes.py',
   'PYMODULE'),
  ('more_itertools.more',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/more_itertools/more.py',
   'PYMODULE'),
  ('json',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/scanner.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('site',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/webbrowser.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shlex.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tty.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/configparser.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_types.py',
   'PYMODULE'),
  ('tomllib._re',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('glob',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/glob.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipimport.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/plistlib.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pkgutil.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/__future__.py',
   'PYMODULE'),
  ('jaraco.context',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/jaraco/context/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('jaraco.functools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('jaraco.classes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/jaraco/classes/__init__.py',
   'PYMODULE'),
  ('jaraco.classes.properties',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/jaraco/classes/properties.py',
   'PYMODULE'),
  ('keyring.backends',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/__init__.py',
   'PYMODULE'),
  ('keyring.backends.fail',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/fail.py',
   'PYMODULE'),
  ('keyring.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/errors.py',
   'PYMODULE'),
  ('keyring.compat.properties',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/compat/properties.py',
   'PYMODULE'),
  ('keyring.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/compat/__init__.py',
   'PYMODULE'),
  ('keyring.backend',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backend.py',
   'PYMODULE'),
  ('keyring.compat.py312',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/compat/py312.py',
   'PYMODULE'),
  ('keyring.util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/util/__init__.py',
   'PYMODULE'),
  ('keyring.util.platform_',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/util/platform_.py',
   'PYMODULE'),
  ('keyring.credentials',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/credentials.py',
   'PYMODULE'),
  ('darkdetect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/darkdetect/__init__.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/darkdetect/_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/darkdetect/_windows_detect.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/darkdetect/_dummy.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/darkdetect/_mac_detect.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/packaging/version.py',
   'PYMODULE'),
  ('tkinter.ttk',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/ttk.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/commondialog.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL._typing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/colorsys.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL._util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_tkinter_finder.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('password_dialog',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/password_dialog.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getpass.py',
   'PYMODULE'),
  ('keyring',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/__init__.py',
   'PYMODULE'),
  ('keyring.backends.null',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/null.py',
   'PYMODULE'),
  ('keyring.backends.macOS.api',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/macOS/api.py',
   'PYMODULE'),
  ('keyring.backends.macOS',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/macOS/__init__.py',
   'PYMODULE'),
  ('keyring.backends.libsecret',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/libsecret.py',
   'PYMODULE'),
  ('keyring.backends.kwallet',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/kwallet.py',
   'PYMODULE'),
  ('keyring.backends.chainer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/chainer.py',
   'PYMODULE'),
  ('keyring.backends.Windows',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/Windows.py',
   'PYMODULE'),
  ('keyring.backends.SecretService',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/backends/SecretService.py',
   'PYMODULE'),
  ('keyring.core',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring/core.py',
   'PYMODULE'),
  ('platform',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/platform.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('PIL.Image',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL._version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('queue',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/queue.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/dialog.py',
   'PYMODULE'),
  ('tkinter',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/constants.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/__init__.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/dom/__init__.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('customtkinter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/utility/utility_functions.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/utility/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/image/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/image/ctk_image.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/core_widget_classes/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/core_rendering/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/core_rendering/draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/core_rendering/ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/theme/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/theme/theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/scaling/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/scaling/scaling_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/scaling/scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/font/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/font/font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/font/ctk_font.py',
   'PYMODULE'),
  ('tkinter.font',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tkinter/font.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/appearance_mode/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/windows/ctk_toplevel.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.12/Python',
   '/Library/Frameworks/Python.framework/Versions/3.12/Python',
   'BINARY'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/syslog.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imagingtk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_webp.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imagingcms.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imagingmath.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imaging.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_tkinter.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libssl.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libtcl8.6.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libtk8.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY')],
 [],
 [],
 [('img/123.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/123.webp',
   'DATA'),
  ('img/bluetooth.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/bluetooth.icns',
   'DATA'),
  ('img/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/device.icns',
   'DATA'),
  ('img/erase.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/erase.png',
   'DATA'),
  ('img/exit.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/exit.png',
   'DATA'),
  ('img/findmy.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/findmy.icns',
   'DATA'),
  ('img/shutdown.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/shutdown.png',
   'DATA'),
  ('img/sysinfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/sysinfo.icns',
   'DATA'),
  ('img/vmt4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vmt4.png',
   'DATA'),
  ('img/vs_icns/A simple and minimalistic image icon featuring the letters "V" '
   'and "S" in a very small size that pertains to a Mac OS system lock '
   'checker. The design should be sleek and modern, emphasizing security and '
   'compatibility with Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/A '
   'simple and minimalistic image icon featuring the letters "V" and "S" in a '
   'very small size that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   'DATA'),
  ('img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   'DATA'),
  ('img/vs_icns/checkADE2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/checkADE2.icns',
   'DATA'),
  ('img/vs_icns/outlook.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/outlook.icns',
   'DATA'),
  ('img/vs_icns/vmt2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vmt2.png',
   'DATA'),
  ('img/vs_icns/vmt3.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vmt3.png',
   'DATA'),
  ('img/vs_icns/vs1.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs1.png',
   'DATA'),
  ('img/vs_icns/vs2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs2.png',
   'DATA'),
  ('img/vs_icns/vs_ade.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade.jpeg',
   'DATA'),
  ('img/vs_icns/vs_ade.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade.webp',
   'DATA'),
  ('img/vs_icns/vs_ade2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade2.png',
   'DATA'),
  ('img/vs_icns/vs_ade4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade4.png',
   'DATA'),
  ('img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vsed '
   'that pertains to a Mac OS system lock checker. The design should be sleek '
   'and modern, emphasizing security and compatibility with Mac OS..png',
   'DATA'),
  ('img/vs_mac_tool_v2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2.icns',
   'DATA'),
  ('img/vs_mac_tool_v2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2.png',
   'DATA'),
  ('img/vs_mac_tool_v2_rounded.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2_rounded.png',
   'DATA'),
  ('img/vsmactool.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vsmactool.png',
   'DATA'),
  ('img/xxx/GenericAirDiskIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/GenericAirDiskIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarDeleteIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/ToolbarDeleteIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarInfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/ToolbarInfo.icns',
   'DATA'),
  ('img/xxx/bluetooth.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/bluetooth.png',
   'DATA'),
  ('img/xxx/bluetooth2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/bluetooth2.png',
   'DATA'),
  ('img/xxx/checkADE.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE.icns',
   'DATA'),
  ('img/xxx/checkADE.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE.png',
   'DATA'),
  ('img/xxx/checkADE2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE2.png',
   'DATA'),
  ('img/xxx/clear.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/clear.png',
   'DATA'),
  ('img/xxx/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/device.icns',
   'DATA'),
  ('img/xxx/panda.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/panda.icns',
   'DATA'),
  ('img/xxx/panda.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/panda.ico',
   'DATA'),
  ('img/xxx/xxx.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/xxx.png',
   'DATA'),
  ('password_dialog.py',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/password_dialog.py',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('keyring-25.6.0.dist-info/LICENSE',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/LICENSE',
   'DATA'),
  ('keyring-25.6.0.dist-info/entry_points.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/entry_points.txt',
   'DATA'),
  ('keyring-25.6.0.dist-info/INSTALLER',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/INSTALLER',
   'DATA'),
  ('keyring-25.6.0.dist-info/METADATA',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/METADATA',
   'DATA'),
  ('keyring-25.6.0.dist-info/RECORD',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/RECORD',
   'DATA'),
  ('keyring-25.6.0.dist-info/REQUESTED',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/REQUESTED',
   'DATA'),
  ('keyring-25.6.0.dist-info/WHEEL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/WHEEL',
   'DATA'),
  ('keyring-25.6.0.dist-info/top_level.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/top_level.txt',
   'DATA'),
  ('_tcl_data/history.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/history.tcl',
   'DATA'),
  ('_tcl_data/msgs/sw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sw.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ph.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_ph.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/msgs/fr_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr_ch.msg',
   'DATA'),
  ('_tcl_data/msgs/es_mx.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_mx.msg',
   'DATA'),
  ('_tk_data/obsolete.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/obsolete.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr_ca.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_ir.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fa_ir.msg',
   'DATA'),
  ('_tcl_data/msgs/fa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fa.msg',
   'DATA'),
  ('_tcl_data/msgs/lv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/lv.msg',
   'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tcl_data/msgs/eu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/eu.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/msgs/pt_br.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/pt_br.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ec.msg',
   'DATA'),
  ('_tcl_data/msgs/ro.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ro.msg',
   'DATA'),
  ('_tcl_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/da.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fa_in.msg',
   'DATA'),
  ('_tk_data/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/spinbox.tcl',
   'DATA'),
  ('_tk_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/ru.msg',
   'DATA'),
  ('_tk_data/ttk/sizegrip.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/sizegrip.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tcl_data/msgs/id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/id.msg',
   'DATA'),
  ('_tk_data/ttk/xpTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/xpTheme.tcl',
   'DATA'),
  ('_tk_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tk_data/msgs/en.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/en.msg',
   'DATA'),
  ('_tcl_data/msgs/ru_ua.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ru_ua.msg',
   'DATA'),
  ('_tk_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/gv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gv.msg',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ko_kr.msg',
   'DATA'),
  ('_tk_data/console.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/console.tcl',
   'DATA'),
  ('_tcl_data/msgs/ta.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ta.msg',
   'DATA'),
  ('_tcl_data/msgs/hr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hr.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/msgs/ms.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ms.msg',
   'DATA'),
  ('_tk_data/xmfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/xmfbox.tcl',
   'DATA'),
  ('tcl8/8.5/tcltest-2.5.7.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.5/tcltest-2.5.7.tm',
   'DATA'),
  ('_tk_data/msgs/zh_cn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tcl_data/package.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/package.tcl',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/sv.msg',
   'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tk_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tcl_data/msgs/en_nz.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_nz.msg',
   'DATA'),
  ('_tcl_data/msgs/kok.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kok.msg',
   'DATA'),
  ('_tcl_data/msgs/fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fo.msg',
   'DATA'),
  ('_tk_data/msgbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/af.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/af.msg',
   'DATA'),
  ('_tcl_data/msgs/ga_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ga_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ca.msg',
   'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/scrollbar.tcl',
   'DATA'),
  ('_tk_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/msgs/sh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sh.msg',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/de_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tcl_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('_tcl_data/msgs/ta_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ta_in.msg',
   'DATA'),
  ('_tcl_data/msgs/mr_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mr_in.msg',
   'DATA'),
  ('_tcl_data/msgs/gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gl.msg',
   'DATA'),
  ('_tcl_data/msgs/is.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/is.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tk_data/text.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/text.tcl',
   'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tcl_data/encoding/tis-620.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('tcl8/8.6/http-2.9.8.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.6/http-2.9.8.tm',
   'DATA'),
  ('_tcl_data/msgs/te_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/te_in.msg',
   'DATA'),
  ('_tcl_data/encoding/macCentEuro.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tk_data/mkpsenc.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/mkpsenc.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_pa.msg',
   'DATA'),
  ('_tcl_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/msgs/ja.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ja.msg',
   'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/msgs/ko.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ko.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tk_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/msgs/kw_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kw_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/es_bo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_bo.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_cr.msg',
   'DATA'),
  ('_tk_data/listbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/listbox.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tcl_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/msgs/tr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/tr.msg',
   'DATA'),
  ('_tcl_data/msgs/es_uy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_uy.msg',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nl_be.msg',
   'DATA'),
  ('_tk_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/cs.msg',
   'DATA'),
  ('_tk_data/ttk/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/he.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/he.msg',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tcl_data/parray.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/parray.tcl',
   'DATA'),
  ('_tk_data/ttk/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_ca.msg',
   'DATA'),
  ('_tk_data/dialog.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/dialog.tcl',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('_tk_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/msgs/zh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh.msg',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tcl_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tcl_data/msgs/lt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/lt.msg',
   'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tk_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/pl.msg',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/aquaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/msgs/bn_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/bn_in.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tk_data/icons.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/icons.tcl',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/clamTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/nn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nn.msg',
   'DATA'),
  ('_tcl_data/msgs/bg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/bg.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_in.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_jo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_jo.msg',
   'DATA'),
  ('_tcl_data/msgs/fo_fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fo_fo.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_ie.msg',
   'DATA'),
  ('_tk_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_cn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tk_data/tk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tk.tcl',
   'DATA'),
  ('_tcl_data/auto.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/auto.tcl',
   'DATA'),
  ('_tk_data/ttk/ttk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/ttk.tcl',
   'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/kl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kl.msg',
   'DATA'),
  ('_tcl_data/msgs/fi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tk_data/ttk/defaults.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/defaults.tcl',
   'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data/msgs/mr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mr.msg',
   'DATA'),
  ('_tcl_data/msgs/en_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_hk.msg',
   'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/msgs/bn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/bn.msg',
   'DATA'),
  ('_tcl_data/tclAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/tclAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/es_pr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_pr.msg',
   'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_zw.msg',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('_tcl_data/msgs/uk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/uk.msg',
   'DATA'),
  ('_tk_data/unsupported.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/unsupported.tcl',
   'DATA'),
  ('_tcl_data/msgs/hi_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hi_in.msg',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/eu_es.msg',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tcl_data/msgs/es_hn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_hn.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ni.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ni.msg',
   'DATA'),
  ('_tk_data/tkfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tkfbox.tcl',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('tcl8/8.4/platform-1.0.19.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.4/platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tcl_data/msgs/es_co.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_co.msg',
   'DATA'),
  ('_tcl_data/clock.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/clock.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tcl_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/init.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/init.tcl',
   'DATA'),
  ('_tk_data/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/entry.tcl',
   'DATA'),
  ('_tk_data/clrpick.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/clrpick.tcl',
   'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/treeview.tcl',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tk_data/msgs/fi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/tm.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/tm.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_za.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tk_data/ttk/cursors.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/cursors.tcl',
   'DATA'),
  ('_tcl_data/word.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/word.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_sv.msg',
   'DATA'),
  ('_tcl_data/msgs/vi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/vi.msg',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('_tk_data/ttk/winTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/winTheme.tcl',
   'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/msgs/kok_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kok_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tk_data/focus.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/focus.tcl',
   'DATA'),
  ('_tk_data/ttk/altTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/altTheme.tcl',
   'DATA'),
  ('_tk_data/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/iconlist.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/iconlist.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr_be.msg',
   'DATA'),
  ('_tcl_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/it_ch.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tk_data/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/msgs/sq.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sq.msg',
   'DATA'),
  ('_tcl_data/msgs/kl_gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kl_gl.msg',
   'DATA'),
  ('_tk_data/bgerror.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/bgerror.tcl',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.5/msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data/msgs/de_at.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/de_at.msg',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tk_data/ttk/combobox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/combobox.tcl',
   'DATA'),
  ('_tk_data/tearoff.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tearoff.tcl',
   'DATA'),
  ('_tk_data/images/README',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/README',
   'DATA'),
  ('_tk_data/images/tai-ku.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/tai-ku.gif',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tk_data/fontchooser.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/fontchooser.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/msgs/gl_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gl_es.msg',
   'DATA'),
  ('_tcl_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tcl_data/msgs/zh_tw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_tw.msg',
   'DATA'),
  ('_tcl_data/msgs/nb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nb.msg',
   'DATA'),
  ('_tk_data/optMenu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/optMenu.tcl',
   'DATA'),
  ('_tcl_data/msgs/gv_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gv_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/sk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sk.msg',
   'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/ar_sy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_sy.msg',
   'DATA'),
  ('_tcl_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tk_data/tkAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tkAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/en_au.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_au.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_lb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_lb.msg',
   'DATA'),
  ('_tcl_data/msgs/en_bw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_bw.msg',
   'DATA'),
  ('_tk_data/images/logo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ar.msg',
   'DATA'),
  ('_tcl_data/msgs/es_py.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_py.msg',
   'DATA'),
  ('_tcl_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/msgs/ms_my.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ms_my.msg',
   'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tcl_data/msgs/sr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sr.msg',
   'DATA'),
  ('_tcl_data/msgs/kw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kw.msg',
   'DATA'),
  ('_tk_data/palette.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/palette.tcl',
   'DATA'),
  ('_tcl_data/msgs/sl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sl.msg',
   'DATA'),
  ('_tk_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/da.msg',
   'DATA'),
  ('_tcl_data/msgs/en_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_sg.msg',
   'DATA'),
  ('_tk_data/images/logo64.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logo64.gif',
   'DATA'),
  ('_tk_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/nl.msg',
   'DATA'),
  ('_tk_data/ttk/progress.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/progress.tcl',
   'DATA'),
  ('_tk_data/ttk/utils.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/utils.tcl',
   'DATA'),
  ('tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tcl_data/msgs/id_id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/id_id.msg',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/notebook.tcl',
   'DATA'),
  ('_tcl_data/msgs/mt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mt.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tk_data/menu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/menu.tcl',
   'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tcl_data/msgs/es_gt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_gt.msg',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/safe.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/safe.tcl',
   'DATA'),
  ('_tcl_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sv.msg',
   'DATA'),
  ('_tk_data/scrlbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/scrlbar.tcl',
   'DATA'),
  ('_tcl_data/msgs/et.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/et.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_in.msg',
   'DATA'),
  ('_tk_data/choosedir.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/choosedir.tcl',
   'DATA'),
  ('_tk_data/megawidget.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/megawidget.tcl',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/spinbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/hi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hi.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tcl_data/msgs/af_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/af_za.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tk_data/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/button.tcl',
   'DATA'),
  ('_tcl_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/eo.msg',
   'DATA'),
  ('_tk_data/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_pe.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_pe.msg',
   'DATA'),
  ('_tcl_data/msgs/es_do.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_do.msg',
   'DATA'),
  ('_tcl_data/msgs/th.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/th.msg',
   'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tk_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/fr.msg',
   'DATA'),
  ('_tk_data/images/logo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logo.eps',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_sg.msg',
   'DATA'),
  ('_tk_data/comdlg.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/comdlg.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/mk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mk.msg',
   'DATA'),
  ('_tk_data/ttk/fonts.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/fonts.tcl',
   'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tk_data/safetk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/safetk.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/msgs/be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/be.msg',
   'DATA'),
  ('_tcl_data/msgs/ga.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ga.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_cl.msg',
   'DATA'),
  ('_tcl_data/msgs/te.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/te.msg',
   'DATA'),
  ('_tcl_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/tclIndex',
   'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ve.msg',
   'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tcl_data/http1.0/http.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/http1.0/http.tcl',
   'DATA'),
  ('_tk_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tclIndex',
   'DATA'),
  ('_tk_data/ttk/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/button.tcl',
   'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter/assets/themes/dark-blue.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/themes/dark-blue.json',
   'DATA'),
  ('customtkinter/assets/themes/green.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/themes/green.json',
   'DATA'),
  ('customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter/assets/themes/blue.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/themes/blue.json',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.12/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/vs_mac_tools_v2/base_library.zip',
   'DATA'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.12/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.12/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.12', 'SYMLINK')])
