([('VS Mac Tools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/vs_mac_tools_v2/VS '
   'Mac Tools',
   'EXECUTABLE'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('Python.framework/Versions/3.12/Python',
   '/Library/Frameworks/Python.framework/Versions/3.12/Python',
   'BINARY'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/syslog.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imagingtk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_webp.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imagingcms.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imagingmath.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-312-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/_imaging.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_tkinter.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libssl.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libtcl8.6.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libtk8.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY'),
  ('img/123.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/123.webp',
   'DATA'),
  ('img/bluetooth.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/bluetooth.icns',
   'DATA'),
  ('img/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/device.icns',
   'DATA'),
  ('img/erase.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/erase.png',
   'DATA'),
  ('img/exit.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/exit.png',
   'DATA'),
  ('img/findmy.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/findmy.icns',
   'DATA'),
  ('img/shutdown.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/shutdown.png',
   'DATA'),
  ('img/sysinfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/sysinfo.icns',
   'DATA'),
  ('img/vmt4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vmt4.png',
   'DATA'),
  ('img/vs_icns/A simple and minimalistic image icon featuring the letters "V" '
   'and "S" in a very small size that pertains to a Mac OS system lock '
   'checker. The design should be sleek and modern, emphasizing security and '
   'compatibility with Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/A '
   'simple and minimalistic image icon featuring the letters "V" and "S" in a '
   'very small size that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   'DATA'),
  ('img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   'DATA'),
  ('img/vs_icns/checkADE2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/checkADE2.icns',
   'DATA'),
  ('img/vs_icns/outlook.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/outlook.icns',
   'DATA'),
  ('img/vs_icns/vmt2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vmt2.png',
   'DATA'),
  ('img/vs_icns/vmt3.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vmt3.png',
   'DATA'),
  ('img/vs_icns/vs1.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs1.png',
   'DATA'),
  ('img/vs_icns/vs2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs2.png',
   'DATA'),
  ('img/vs_icns/vs_ade.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade.jpeg',
   'DATA'),
  ('img/vs_icns/vs_ade.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade.webp',
   'DATA'),
  ('img/vs_icns/vs_ade2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade2.png',
   'DATA'),
  ('img/vs_icns/vs_ade4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vs_ade4.png',
   'DATA'),
  ('img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_icns/vsed '
   'that pertains to a Mac OS system lock checker. The design should be sleek '
   'and modern, emphasizing security and compatibility with Mac OS..png',
   'DATA'),
  ('img/vs_mac_tool_v2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2.icns',
   'DATA'),
  ('img/vs_mac_tool_v2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2.png',
   'DATA'),
  ('img/vs_mac_tool_v2_rounded.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vs_mac_tool_v2_rounded.png',
   'DATA'),
  ('img/vsmactool.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vsmactool.png',
   'DATA'),
  ('img/xxx/GenericAirDiskIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/GenericAirDiskIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarDeleteIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/ToolbarDeleteIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarInfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/ToolbarInfo.icns',
   'DATA'),
  ('img/xxx/bluetooth.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/bluetooth.png',
   'DATA'),
  ('img/xxx/bluetooth2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/bluetooth2.png',
   'DATA'),
  ('img/xxx/checkADE.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE.icns',
   'DATA'),
  ('img/xxx/checkADE.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE.png',
   'DATA'),
  ('img/xxx/checkADE2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/checkADE2.png',
   'DATA'),
  ('img/xxx/clear.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/clear.png',
   'DATA'),
  ('img/xxx/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/device.icns',
   'DATA'),
  ('img/xxx/panda.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/panda.icns',
   'DATA'),
  ('img/xxx/panda.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/panda.ico',
   'DATA'),
  ('img/xxx/xxx.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/xxx/xxx.png',
   'DATA'),
  ('password_dialog.py',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/password_dialog.py',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('keyring-25.6.0.dist-info/LICENSE',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/LICENSE',
   'DATA'),
  ('keyring-25.6.0.dist-info/entry_points.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/entry_points.txt',
   'DATA'),
  ('keyring-25.6.0.dist-info/INSTALLER',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/INSTALLER',
   'DATA'),
  ('keyring-25.6.0.dist-info/METADATA',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/METADATA',
   'DATA'),
  ('keyring-25.6.0.dist-info/RECORD',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/RECORD',
   'DATA'),
  ('keyring-25.6.0.dist-info/REQUESTED',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/REQUESTED',
   'DATA'),
  ('keyring-25.6.0.dist-info/WHEEL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/WHEEL',
   'DATA'),
  ('keyring-25.6.0.dist-info/top_level.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/keyring-25.6.0.dist-info/top_level.txt',
   'DATA'),
  ('_tcl_data/history.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/history.tcl',
   'DATA'),
  ('_tcl_data/msgs/sw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sw.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ph.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_ph.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/msgs/fr_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr_ch.msg',
   'DATA'),
  ('_tcl_data/msgs/es_mx.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_mx.msg',
   'DATA'),
  ('_tk_data/obsolete.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/obsolete.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr_ca.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_ir.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fa_ir.msg',
   'DATA'),
  ('_tcl_data/msgs/fa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fa.msg',
   'DATA'),
  ('_tcl_data/msgs/lv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/lv.msg',
   'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tcl_data/msgs/eu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/eu.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/msgs/pt_br.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/pt_br.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ec.msg',
   'DATA'),
  ('_tcl_data/msgs/ro.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ro.msg',
   'DATA'),
  ('_tcl_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/da.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fa_in.msg',
   'DATA'),
  ('_tk_data/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/spinbox.tcl',
   'DATA'),
  ('_tk_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/ru.msg',
   'DATA'),
  ('_tk_data/ttk/sizegrip.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/sizegrip.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tcl_data/msgs/id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/id.msg',
   'DATA'),
  ('_tk_data/ttk/xpTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/xpTheme.tcl',
   'DATA'),
  ('_tk_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tk_data/msgs/en.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/en.msg',
   'DATA'),
  ('_tcl_data/msgs/ru_ua.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ru_ua.msg',
   'DATA'),
  ('_tk_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/gv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gv.msg',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ko_kr.msg',
   'DATA'),
  ('_tk_data/console.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/console.tcl',
   'DATA'),
  ('_tcl_data/msgs/ta.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ta.msg',
   'DATA'),
  ('_tcl_data/msgs/hr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hr.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/msgs/ms.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ms.msg',
   'DATA'),
  ('_tk_data/xmfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/xmfbox.tcl',
   'DATA'),
  ('tcl8/8.5/tcltest-2.5.7.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.5/tcltest-2.5.7.tm',
   'DATA'),
  ('_tk_data/msgs/zh_cn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tcl_data/package.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/package.tcl',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/sv.msg',
   'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tk_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tcl_data/msgs/en_nz.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_nz.msg',
   'DATA'),
  ('_tcl_data/msgs/kok.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kok.msg',
   'DATA'),
  ('_tcl_data/msgs/fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fo.msg',
   'DATA'),
  ('_tk_data/msgbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/af.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/af.msg',
   'DATA'),
  ('_tcl_data/msgs/ga_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ga_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ca.msg',
   'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/scrollbar.tcl',
   'DATA'),
  ('_tk_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/msgs/sh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sh.msg',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/de_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tcl_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('_tcl_data/msgs/ta_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ta_in.msg',
   'DATA'),
  ('_tcl_data/msgs/mr_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mr_in.msg',
   'DATA'),
  ('_tcl_data/msgs/gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gl.msg',
   'DATA'),
  ('_tcl_data/msgs/is.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/is.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tk_data/text.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/text.tcl',
   'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tcl_data/encoding/tis-620.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('tcl8/8.6/http-2.9.8.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.6/http-2.9.8.tm',
   'DATA'),
  ('_tcl_data/msgs/te_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/te_in.msg',
   'DATA'),
  ('_tcl_data/encoding/macCentEuro.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tk_data/mkpsenc.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/mkpsenc.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_pa.msg',
   'DATA'),
  ('_tcl_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/msgs/ja.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ja.msg',
   'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/msgs/ko.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ko.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tk_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/msgs/kw_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kw_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/es_bo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_bo.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_cr.msg',
   'DATA'),
  ('_tk_data/listbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/listbox.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tcl_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/msgs/tr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/tr.msg',
   'DATA'),
  ('_tcl_data/msgs/es_uy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_uy.msg',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nl_be.msg',
   'DATA'),
  ('_tk_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/cs.msg',
   'DATA'),
  ('_tk_data/ttk/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/he.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/he.msg',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tcl_data/parray.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/parray.tcl',
   'DATA'),
  ('_tk_data/ttk/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_ca.msg',
   'DATA'),
  ('_tk_data/dialog.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/dialog.tcl',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('_tk_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/msgs/zh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh.msg',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tcl_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tcl_data/msgs/lt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/lt.msg',
   'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tk_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/pl.msg',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/aquaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/msgs/bn_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/bn_in.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tk_data/icons.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/icons.tcl',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/clamTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/nn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nn.msg',
   'DATA'),
  ('_tcl_data/msgs/bg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/bg.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_in.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_jo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_jo.msg',
   'DATA'),
  ('_tcl_data/msgs/fo_fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fo_fo.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_ie.msg',
   'DATA'),
  ('_tk_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_cn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tk_data/tk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tk.tcl',
   'DATA'),
  ('_tcl_data/auto.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/auto.tcl',
   'DATA'),
  ('_tk_data/ttk/ttk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/ttk.tcl',
   'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/kl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kl.msg',
   'DATA'),
  ('_tcl_data/msgs/fi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tk_data/ttk/defaults.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/defaults.tcl',
   'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data/msgs/mr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mr.msg',
   'DATA'),
  ('_tcl_data/msgs/en_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_hk.msg',
   'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/msgs/bn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/bn.msg',
   'DATA'),
  ('_tcl_data/tclAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/tclAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/es_pr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_pr.msg',
   'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_zw.msg',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('_tcl_data/msgs/uk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/uk.msg',
   'DATA'),
  ('_tk_data/unsupported.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/unsupported.tcl',
   'DATA'),
  ('_tcl_data/msgs/hi_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hi_in.msg',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/eu_es.msg',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tcl_data/msgs/es_hn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_hn.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ni.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ni.msg',
   'DATA'),
  ('_tk_data/tkfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tkfbox.tcl',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('tcl8/8.4/platform-1.0.19.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.4/platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tcl_data/msgs/es_co.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_co.msg',
   'DATA'),
  ('_tcl_data/clock.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/clock.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tcl_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/init.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/init.tcl',
   'DATA'),
  ('_tk_data/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/entry.tcl',
   'DATA'),
  ('_tk_data/clrpick.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/clrpick.tcl',
   'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/treeview.tcl',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tk_data/msgs/fi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/tm.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/tm.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_za.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tk_data/ttk/cursors.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/cursors.tcl',
   'DATA'),
  ('_tcl_data/word.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/word.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_sv.msg',
   'DATA'),
  ('_tcl_data/msgs/vi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/vi.msg',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('_tk_data/ttk/winTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/winTheme.tcl',
   'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/msgs/kok_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kok_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tk_data/focus.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/focus.tcl',
   'DATA'),
  ('_tk_data/ttk/altTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/altTheme.tcl',
   'DATA'),
  ('_tk_data/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/iconlist.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/iconlist.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/fr_be.msg',
   'DATA'),
  ('_tcl_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/it_ch.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tk_data/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/msgs/sq.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sq.msg',
   'DATA'),
  ('_tcl_data/msgs/kl_gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kl_gl.msg',
   'DATA'),
  ('_tk_data/bgerror.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/bgerror.tcl',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.5/msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data/msgs/de_at.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/de_at.msg',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tk_data/ttk/combobox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/combobox.tcl',
   'DATA'),
  ('_tk_data/tearoff.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tearoff.tcl',
   'DATA'),
  ('_tk_data/images/README',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/README',
   'DATA'),
  ('_tk_data/images/tai-ku.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/tai-ku.gif',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tk_data/fontchooser.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/fontchooser.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/msgs/gl_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gl_es.msg',
   'DATA'),
  ('_tcl_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tcl_data/msgs/zh_tw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_tw.msg',
   'DATA'),
  ('_tcl_data/msgs/nb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nb.msg',
   'DATA'),
  ('_tk_data/optMenu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/optMenu.tcl',
   'DATA'),
  ('_tcl_data/msgs/gv_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/gv_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/sk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sk.msg',
   'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/ar_sy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_sy.msg',
   'DATA'),
  ('_tcl_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tk_data/tkAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tkAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/en_au.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_au.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_lb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ar_lb.msg',
   'DATA'),
  ('_tcl_data/msgs/en_bw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_bw.msg',
   'DATA'),
  ('_tk_data/images/logo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ar.msg',
   'DATA'),
  ('_tcl_data/msgs/es_py.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_py.msg',
   'DATA'),
  ('_tcl_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/msgs/ms_my.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ms_my.msg',
   'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tcl_data/msgs/sr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sr.msg',
   'DATA'),
  ('_tcl_data/msgs/kw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/kw.msg',
   'DATA'),
  ('_tk_data/palette.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/palette.tcl',
   'DATA'),
  ('_tcl_data/msgs/sl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sl.msg',
   'DATA'),
  ('_tk_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/da.msg',
   'DATA'),
  ('_tcl_data/msgs/en_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_sg.msg',
   'DATA'),
  ('_tk_data/images/logo64.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logo64.gif',
   'DATA'),
  ('_tk_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/nl.msg',
   'DATA'),
  ('_tk_data/ttk/progress.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/progress.tcl',
   'DATA'),
  ('_tk_data/ttk/utils.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/utils.tcl',
   'DATA'),
  ('tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tcl_data/msgs/id_id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/id_id.msg',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/notebook.tcl',
   'DATA'),
  ('_tcl_data/msgs/mt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mt.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tk_data/menu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/menu.tcl',
   'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tcl_data/msgs/es_gt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_gt.msg',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/safe.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/safe.tcl',
   'DATA'),
  ('_tcl_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/sv.msg',
   'DATA'),
  ('_tk_data/scrlbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/scrlbar.tcl',
   'DATA'),
  ('_tcl_data/msgs/et.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/et.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/en_in.msg',
   'DATA'),
  ('_tk_data/choosedir.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/choosedir.tcl',
   'DATA'),
  ('_tk_data/megawidget.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/megawidget.tcl',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/spinbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/hi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/hi.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tcl_data/msgs/af_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/af_za.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tk_data/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/button.tcl',
   'DATA'),
  ('_tcl_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/eo.msg',
   'DATA'),
  ('_tk_data/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_pe.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_pe.msg',
   'DATA'),
  ('_tcl_data/msgs/es_do.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_do.msg',
   'DATA'),
  ('_tcl_data/msgs/th.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/th.msg',
   'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tk_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/fr.msg',
   'DATA'),
  ('_tk_data/images/logo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/logo.eps',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_sg.msg',
   'DATA'),
  ('_tk_data/comdlg.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/comdlg.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/zh_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/mk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/mk.msg',
   'DATA'),
  ('_tk_data/ttk/fonts.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/fonts.tcl',
   'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tk_data/safetk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/safetk.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/msgs/be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/be.msg',
   'DATA'),
  ('_tcl_data/msgs/ga.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/ga.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_cl.msg',
   'DATA'),
  ('_tcl_data/msgs/te.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/te.msg',
   'DATA'),
  ('_tcl_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/tclIndex',
   'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/msgs/es_ve.msg',
   'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tcl_data/http1.0/http.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/http1.0/http.tcl',
   'DATA'),
  ('_tk_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/tclIndex',
   'DATA'),
  ('_tk_data/ttk/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tk8.6/ttk/button.tcl',
   'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter/assets/themes/dark-blue.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/themes/dark-blue.json',
   'DATA'),
  ('customtkinter/assets/themes/green.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/themes/green.json',
   'DATA'),
  ('customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter/assets/themes/blue.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/themes/blue.json',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.12/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.12/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/vs_mac_tools_v2/base_library.zip',
   'DATA'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.12/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.12/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.12', 'SYMLINK')],)
