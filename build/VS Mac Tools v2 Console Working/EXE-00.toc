('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS Mac '
 'Tools v2 Console Working/VS Mac Tools v2 Console Working',
 True,
 False,
 True,
 ['/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/img/vsmactool.icns'],
 None,
 False,
 False,
 None,
 True,
 Fals<PERSON>,
 'arm64',
 None,
 None,
 '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS Mac '
 'Tools v2 Console Working/VS Mac Tools v2 Console Working.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Console Working/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Console Working/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Console Working/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Console Working/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Console Working/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('vs_mac_tools_v2',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/vs_mac_tools_v2.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1749447797,
 [('run',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/bootloader/Darwin-64bit/run',
   'EXECUTABLE')],
 '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3')
