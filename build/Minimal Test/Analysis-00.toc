(['/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/minimal_test_app.py'],
 ['/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker'],
 ['customtkinter'],
 [('/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('customtkinter/assets/.DS_Store',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/.DS_Store',
   'DATA'),
  ('customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   'DATA'),
  ('customtkinter/assets/icons/.DS_Store',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/icons/.DS_Store',
   'DATA'),
  ('customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter/assets/themes/blue.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/themes/blue.json',
   'DATA'),
  ('customtkinter/assets/themes/dark-blue.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/themes/dark-blue.json',
   'DATA'),
  ('customtkinter/assets/themes/green.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/themes/green.json',
   'DATA')],
 '3.9.6 (default, Apr 30 2025, 02:07:17) \n[Clang 17.0.0 (clang-1700.0.13.5)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('minimal_test_app',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/minimal_test_app.py',
   'PYSOURCE')],
 [('subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('threading',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('signal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/signal.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._common',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('random',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/random.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('string',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/string.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/typing.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('email',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('socket',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/base64.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('uu',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uu.py',
   'PYMODULE'),
  ('optparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('csv',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/csv.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/token.py',
   'PYMODULE'),
  ('struct',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/struct.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bz2.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('copy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copy.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('dis',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('ast',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ast.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/version.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/jaraco/functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/_vendor/__init__.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pkg_resources/extern/__init__.py',
   'PYMODULE'),
  ('imp',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/imp.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('platform',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/platform.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('customtkinter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/utility/utility_functions.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/utility/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/image/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/image/ctk_image.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.char',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('doctest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('pdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pdb.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tty.py',
   'PYMODULE'),
  ('glob',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/glob.py',
   'PYMODULE'),
  ('code',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('bdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bdb.py',
   'PYMODULE'),
  ('cmd',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/cmd.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('json',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/colorsys.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL.Image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/__init__.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/core_widget_classes/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py',
   'PYMODULE'),
  ('tkinter.ttk',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/ttk.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/core_rendering/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/core_rendering/draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/core_rendering/ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/theme/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/theme/theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/scaling/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/scaling/scaling_tracker.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/scaling/scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/font/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/font/font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/font/ctk_font.py',
   'PYMODULE'),
  ('tkinter.font',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/font.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/appearance_mode/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py',
   'PYMODULE'),
  ('darkdetect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/darkdetect/__init__.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/darkdetect/_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/darkdetect/_windows_detect.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/darkdetect/_dummy.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/darkdetect/_mac_detect.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/windows/ctk_toplevel.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/simpledialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/dialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/__init__.py',
   'PYMODULE')],
 [('Python3.framework/Versions/3.9/Python3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3',
   'BINARY'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_imagingtk.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/mtrand.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_sfc64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_philox.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pcg64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_mt19937.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/bit_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_bounded_integers.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_common.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_webp.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_imagingcms.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_imagingmath.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_elementtree.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_imaging.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_tkinter.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY')],
 [],
 [],
 [('customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   'DATA'),
  ('customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter/assets/themes/blue.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/themes/blue.json',
   'DATA'),
  ('customtkinter/assets/themes/dark-blue.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/themes/dark-blue.json',
   'DATA'),
  ('customtkinter/assets/themes/green.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/customtkinter/assets/themes/green.json',
   'DATA'),
  ('Python3', 'Python3.framework/Versions/3.9/Python3', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/Minimal '
   'Test/base_library.zip',
   'DATA'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK'),
  ('Python3.framework/Python3', 'Versions/Current/Python3', 'SYMLINK'),
  ('Python3.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python3.framework/Versions/3.9/Resources/Info.plist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Info.plist',
   'DATA'),
  ('Python3.framework/Versions/Current', '3.9', 'SYMLINK')])
