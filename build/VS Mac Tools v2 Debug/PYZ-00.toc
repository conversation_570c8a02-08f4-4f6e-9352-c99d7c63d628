('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS Mac '
 'Tools v2 Debug/PYZ-00.pyz',
 [('PIL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('PIL._util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('PIL.features',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PIL/features.py',
   'PYMODULE'),
  ('__future__',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_ios_support',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('_osx_support',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('ast',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('base64',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/base64.py',
   'PYMODULE'),
  ('bisect',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('code',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/code.py',
   'PYMODULE'),
  ('codeop',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codeop.py',
   'PYMODULE'),
  ('colorsys',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/colorsys.py',
   'PYMODULE'),
  ('concurrent',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copy.py',
   'PYMODULE'),
  ('csv',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/has_key.py',
   'PYMODULE'),
  ('customtkinter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/ctk_toplevel.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/appearance_mode/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/core_rendering/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/core_rendering/ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/core_rendering/draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/core_widget_classes/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/font/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/font/ctk_font.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/font/font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/image/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/image/ctk_image.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/scaling/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/scaling/scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/scaling/scaling_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/theme/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/theme/theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/utility/__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/customtkinter/windows/widgets/utility/utility_functions.py',
   'PYMODULE'),
  ('darkdetect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/darkdetect/__init__.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/darkdetect/_dummy.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/darkdetect/_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/darkdetect/_mac_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/darkdetect/_windows_detect.py',
   'PYMODULE'),
  ('dataclasses',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/difflib.py',
   'PYMODULE'),
  ('dis',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dis.py',
   'PYMODULE'),
  ('email',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/glob.py',
   'PYMODULE'),
  ('gzip',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hmac.py',
   'PYMODULE'),
  ('html',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/entities.py',
   'PYMODULE'),
  ('http',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/cookiejar.py',
   'PYMODULE'),
  ('http.server',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py',
   'PYMODULE'),
  ('importlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.classes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/jaraco/classes/__init__.py',
   'PYMODULE'),
  ('jaraco.classes.properties',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/jaraco/classes/properties.py',
   'PYMODULE'),
  ('jaraco.context',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/jaraco/context/__init__.py',
   'PYMODULE'),
  ('jaraco.functools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('json',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('keyring',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/__init__.py',
   'PYMODULE'),
  ('keyring.backend',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backend.py',
   'PYMODULE'),
  ('keyring.backends',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/__init__.py',
   'PYMODULE'),
  ('keyring.backends.SecretService',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/SecretService.py',
   'PYMODULE'),
  ('keyring.backends.Windows',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/Windows.py',
   'PYMODULE'),
  ('keyring.backends.chainer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/chainer.py',
   'PYMODULE'),
  ('keyring.backends.fail',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/fail.py',
   'PYMODULE'),
  ('keyring.backends.kwallet',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/kwallet.py',
   'PYMODULE'),
  ('keyring.backends.libsecret',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/libsecret.py',
   'PYMODULE'),
  ('keyring.backends.macOS',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/macOS/__init__.py',
   'PYMODULE'),
  ('keyring.backends.macOS.api',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/macOS/api.py',
   'PYMODULE'),
  ('keyring.backends.null',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/backends/null.py',
   'PYMODULE'),
  ('keyring.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/compat/__init__.py',
   'PYMODULE'),
  ('keyring.compat.properties',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/compat/properties.py',
   'PYMODULE'),
  ('keyring.compat.py312',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/compat/py312.py',
   'PYMODULE'),
  ('keyring.core',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/core.py',
   'PYMODULE'),
  ('keyring.credentials',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/credentials.py',
   'PYMODULE'),
  ('keyring.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/errors.py',
   'PYMODULE'),
  ('keyring.util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/util/__init__.py',
   'PYMODULE'),
  ('keyring.util.platform_',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/keyring/util/platform_.py',
   'PYMODULE'),
  ('logging',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/mimetypes.py',
   'PYMODULE'),
  ('more_itertools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/more_itertools/__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/more_itertools/more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/more_itertools/recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/opcode.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('password_dialog',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/password_dialog.py',
   'PYMODULE'),
  ('pathlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickle.py',
   'PYMODULE'),
  ('pkgutil',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('pydoc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('queue',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/queue.py',
   'PYMODULE'),
  ('quopri',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/quopri.py',
   'PYMODULE'),
  ('random',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/random.py',
   'PYMODULE'),
  ('rlcompleter',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('shlex',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shutil.py',
   'PYMODULE'),
  ('signal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/signal.py',
   'PYMODULE'),
  ('site',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site.py',
   'PYMODULE'),
  ('sitecustomize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sitecustomize.py',
   'PYMODULE'),
  ('socket',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py',
   'PYMODULE'),
  ('ssl',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('string',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/string.py',
   'PYMODULE'),
  ('stringprep',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sysconfig/__init__.py',
   'PYMODULE'),
  ('tarfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py',
   'PYMODULE'),
  ('token',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/token.py',
   'PYMODULE'),
  ('tokenize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('tomllib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_re.py',
   'PYMODULE'),
  ('tomllib._types',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_types.py',
   'PYMODULE'),
  ('tracemalloc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('tty',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tty.py',
   'PYMODULE'),
  ('typing',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/typing.py',
   'PYMODULE'),
  ('unittest',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/main.py',
   'PYMODULE'),
  ('unittest.mock',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/mock.py',
   'PYMODULE'),
  ('unittest.result',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/response.py',
   'PYMODULE'),
  ('vs_mac_tools_v2',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/vs_mac_tools_v2.py',
   'PYMODULE'),
  ('webbrowser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/__init__.py',
   'PYMODULE'),
  ('xml.dom',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('zipimport',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipimport.py',
   'PYMODULE')])
