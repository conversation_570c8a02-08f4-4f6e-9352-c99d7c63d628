('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS Mac '
 'Tools v2 Debug/VS Mac Tools v2 Debug.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Debug/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Debug/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Debug/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Debug/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/build/VS '
   'Mac Tools v2 Debug/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('debug_vs_mac_tools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/debug_vs_mac_tools.py',
   'PYSOURCE')],
 'Python',
 True,
 False,
 False,
 [],
 'arm64',
 None,
 None)
