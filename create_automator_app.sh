#!/bin/bash

# Script to create an Automator app for VS Mac Tools v2

echo "Creating Automator app for VS Mac Tools v2..."

# Create the Automator workflow
cat > /tmp/vs_mac_tools_workflow.applescript << 'EOF'
tell application "Automator"
    set newWorkflow to make new workflow with properties {name:"VS Mac Tools"}
    
    tell newWorkflow
        set newAction to make new action with properties {name:"Run Shell Script"}
        tell newAction
            set value of setting "inputMethod" to 0
            set value of setting "shell" to "/bin/bash"
            set value of setting "source" to "#!/bin/bash

# Get the directory where the app is located  
APP_DIR=\"$(dirname \"$0\")\"
cd \"$APP_DIR\"

# Check if we need to set up the environment
if [ ! -d \"venv\" ]; then
    osascript -e 'display dialog \"Setting up VS Mac Tools for first run. This may take a moment...\" buttons {\"OK\"} default button \"OK\" with icon note'
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install customtkinter pillow keyring darkdetect
else
    source venv/bin/activate
fi

# Run the application
python3 vs_mac_tools_v2.py"
        end tell
    end tell
    
    save newWorkflow in file ((path to desktop as text) & "VS Mac Tools.app") as application
    close newWorkflow
end tell
EOF

# Run the AppleScript to create the workflow
osascript /tmp/vs_mac_tools_workflow.applescript

# Clean up
rm /tmp/vs_mac_tools_workflow.applescript

echo "Automator app created on Desktop as 'VS Mac Tools.app'"
echo "You can now move this app to your Applications folder or anywhere you like."
