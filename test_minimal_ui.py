#!/usr/bin/env python3
"""
Minimal CustomTkinter test to isolate the blank UI issue
"""

import customtkinter as ctk
import sys
import os

def test_basic_customtkinter():
    """Test basic CustomTkinter functionality"""
    print("Testing basic CustomTkinter...")
    
    try:
        # Set appearance mode
        ctk.set_appearance_mode("dark")
        
        # Create basic app
        app = ctk.CTk()
        app.title("Minimal Test")
        app.geometry("400x300")
        
        # Add a simple label
        label = ctk.CTkLabel(app, text="Hello CustomTkinter!", font=("Arial", 20))
        label.pack(pady=50)
        
        # Add a button
        button = ctk.CTkButton(app, text="Test Button", command=lambda: print("Button clicked!"))
        button.pack(pady=20)
        
        print("✅ Basic CustomTkinter app created successfully")
        
        # Show for 3 seconds then close
        app.after(3000, app.quit)
        app.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Basic CustomTkinter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complex_layout():
    """Test more complex layout similar to main app"""
    print("Testing complex layout...")
    
    try:
        ctk.set_appearance_mode("dark")
        
        app = ctk.CTk()
        app.title("Complex Layout Test")
        app.geometry("800x600")
        
        # Configure grid
        app.grid_columnconfigure(1, weight=1)
        
        # Left frame
        left_frame = ctk.CTkFrame(app, width=200, height=400)
        left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        left_frame.grid_propagate(False)
        
        # Add content to left frame
        ctk.CTkLabel(left_frame, text="Left Panel", font=("Arial", 16, "bold")).pack(pady=10)
        
        for i in range(5):
            ctk.CTkButton(left_frame, text=f"Button {i+1}").pack(pady=5, padx=10, fill="x")
        
        # Right frame
        right_frame = ctk.CTkFrame(app)
        right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        right_frame.grid_columnconfigure(0, weight=1)
        right_frame.grid_rowconfigure(0, weight=1)
        
        # Add content to right frame
        ctk.CTkLabel(right_frame, text="Right Panel", font=("Arial", 16, "bold")).grid(row=0, column=0, pady=10)
        
        text_box = ctk.CTkTextbox(right_frame, width=400, height=300)
        text_box.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        text_box.insert("1.0", "This is a test textbox\nLine 2\nLine 3")
        
        print("✅ Complex layout created successfully")
        
        # Show for 3 seconds then close
        app.after(3000, app.quit)
        app.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Complex layout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app_import():
    """Test importing and creating the main app"""
    print("Testing main app import...")
    
    try:
        # Import the main app
        import vs_mac_tools_v2
        print("✅ Main app imported successfully")
        
        # Try to create the app instance
        print("Creating main app instance...")
        app = vs_mac_tools_v2.InternetCheckerApp()
        print("✅ Main app instance created")
        
        # Check if key attributes exist
        attributes_to_check = [
            'process_frame', 'hardware_frame', 'shortcuts_frame', 
            'result_text', 'checkboxes', 'hardware_info'
        ]
        
        for attr in attributes_to_check:
            if hasattr(app, attr):
                print(f"✅ {attr} exists")
            else:
                print(f"❌ {attr} missing")
        
        # Try to update the app
        print("Updating app...")
        app.update()
        print("✅ App updated successfully")
        
        # Check geometry
        geometry = app.geometry()
        print(f"App geometry: {geometry}")
        
        # Try to show for a moment
        app.after(2000, app.quit)
        print("Starting mainloop for 2 seconds...")
        app.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Main app test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("CustomTkinter UI Diagnosis")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    
    # Test 1: Basic CustomTkinter
    print("\n" + "=" * 40)
    print("TEST 1: Basic CustomTkinter")
    print("=" * 40)
    basic_success = test_basic_customtkinter()
    
    # Test 2: Complex Layout
    print("\n" + "=" * 40)
    print("TEST 2: Complex Layout")
    print("=" * 40)
    complex_success = test_complex_layout()
    
    # Test 3: Main App
    print("\n" + "=" * 40)
    print("TEST 3: Main App Import")
    print("=" * 40)
    main_app_success = test_main_app_import()
    
    # Summary
    print("\n" + "=" * 60)
    print("DIAGNOSIS SUMMARY")
    print("=" * 60)
    print(f"Basic CustomTkinter: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"Complex Layout: {'✅ PASS' if complex_success else '❌ FAIL'}")
    print(f"Main App: {'✅ PASS' if main_app_success else '❌ FAIL'}")
    
    if basic_success and complex_success and not main_app_success:
        print("\n🔍 DIAGNOSIS: Issue is specific to the main app code")
        print("💡 RECOMMENDATION: Check main app initialization, imports, or specific CustomTkinter usage")
    elif not basic_success:
        print("\n🔍 DIAGNOSIS: Basic CustomTkinter issue")
        print("💡 RECOMMENDATION: Check CustomTkinter installation or Python environment")
    else:
        print("\n🔍 DIAGNOSIS: All tests passed - issue may be in bundled app environment")
        print("💡 RECOMMENDATION: Check PyInstaller bundling or app execution environment")

if __name__ == "__main__":
    main()
