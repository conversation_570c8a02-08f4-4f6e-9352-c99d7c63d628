#!/usr/bin/env python3
"""
Simple test app to verify <PERSON>y<PERSON>nstalle<PERSON> works with tkinter
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def main():
    try:
        # Create main window
        root = tk.Tk()
        root.title("Simple Test App")
        root.geometry("400x300")
        
        # Add a label
        label = tk.Label(root, text="Hello! This is a test app.", font=("Arial", 16))
        label.pack(pady=20)
        
        # Add a button
        def show_message():
            messagebox.showinfo("Test", "PyInstaller app is working!")
        
        button = tk.Button(root, text="Test Button", command=show_message, font=("Arial", 12))
        button.pack(pady=10)
        
        # Add some system info
        info_text = f"Python version: {sys.version}\nRunning from: {sys.executable}"
        info_label = tk.Label(root, text=info_text, font=("Arial", 10), justify=tk.LEFT)
        info_label.pack(pady=20)
        
        # Start the main loop
        root.mainloop()
        
    except Exception as e:
        # If GUI fails, try to show error in terminal
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Try to show error dialog if possible
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            messagebox.showerror("Error", f"Application error: {str(e)}")
        except:
            pass
        
        sys.exit(1)

if __name__ == "__main__":
    main()
