# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for VS Mac Tools v2
"""

import os
import sys
from pathlib import Path

block_cipher = None

# Get the directory of this spec file
spec_dir = os.path.dirname(os.path.abspath('vs_mac_tools_v2.spec'))

# Define paths
img_dir = os.path.join(spec_dir, 'img')
icon_file = os.path.join(img_dir, 'vs_mac_tool_v2.icns')

# Check if icon file exists, fallback to another icon if not
if not os.path.exists(icon_file):
    icon_file = os.path.join(img_dir, 'vs_icns', 'checkADE2.icns')
    if not os.path.exists(icon_file):
        icon_file = os.path.join(img_dir, 'bluetooth.icns')
        if not os.path.exists(icon_file):
            icon_file = None
            print("Warning: No icon file found")

a = Analysis(
    ['vs_mac_tools_v2.py'],
    pathex=[spec_dir],
    binaries=[],
    datas=[
        ('img', 'img'),
        ('password_dialog.py', '.'),
        ('img/bluetooth.icns', 'img'),
        ('img/findmy.icns', 'img'),
        ('img/device.icns', 'img'),
        ('img/sysinfo.icns', 'img'),
        ('img/shutdown.png', 'img'),
        ('img/erase.png', 'img'),
        ('img/exit.png', 'img'),
        ('img/vs_icns/checkADE2.icns', 'img/vs_icns'),
    ],
    hiddenimports=[
        'customtkinter',
        'customtkinter.windows',
        'customtkinter.windows.widgets',
        'PIL',
        'PIL._tkinter_finder',
        'PIL.Image',
        'PIL.ImageTk',
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.ttk',
        'xml.dom.minidom',
        'xml.etree.ElementTree',
        'packaging.version',
        'darkdetect',
        'keyring',
        'keyring.backends',
        'keyring.backends.macOS',
        'jaraco.classes',
        'jaraco.functools',
        'jaraco.context',
        'getpass',
        'platform',
        'socket',
        'subprocess',
        'threading',
        'os',
        'time',
        'csv',
        'datetime',
        'queue',
        'pathlib',
        'password_dialog',
        'sys',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'numpy', 'scipy', 'pandas', 'PyQt5', 'PyQt6', 'PySide2', 'PySide6'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VS Mac Tools',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=True,  # Enable argv emulation for macOS
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_file,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VS Mac Tools',
)

app = BUNDLE(
    coll,
    name='VS Mac Tools.app',
    icon=icon_file,
    bundle_identifier='com.vonzki.vsmactools',
    version='2.0',
    info_plist={
        'CFBundleName': 'VS Mac Tools',
        'CFBundleDisplayName': 'VS Mac Tools',
        'CFBundleGetInfoString': 'VS Mac Tools v2.0',
        'CFBundleIdentifier': 'com.vonzki.vsmactools',
        'CFBundleVersion': '2.0',
        'CFBundleShortVersionString': '2.0',
        'NSHumanReadableCopyright': '© 2024 by vonzki',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.15',  # macOS Catalina for compatibility
        'NSRequiresAquaSystemAppearance': False,  # Support dark mode
        'NSPrincipalClass': 'NSApplication',  # Required for proper macOS integration
        'NSAppleScriptEnabled': False,
        'LSApplicationCategoryType': 'public.app-category.utilities',
    },
)
