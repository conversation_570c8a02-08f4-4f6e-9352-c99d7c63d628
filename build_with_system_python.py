#!/usr/bin/env python3
"""
Build VS Mac Tools v2 using system Python with tkinter support
"""

import os
import sys
import subprocess
import shutil

def find_system_python():
    """Find system Python with tkinter support"""
    python_paths = [
        '/usr/bin/python3',
        '/opt/homebrew/bin/python3',
        '/usr/local/bin/python3',
        '/System/Library/Frameworks/Python.framework/Versions/3.9/bin/python3',
        '/Library/Frameworks/Python.framework/Versions/3.9/bin/python3',
        '/Library/Frameworks/Python.framework/Versions/3.10/bin/python3',
        '/Library/Frameworks/Python.framework/Versions/3.11/bin/python3',
        '/Library/Frameworks/Python.framework/Versions/3.12/bin/python3',
    ]
    
    for python_path in python_paths:
        if os.path.exists(python_path):
            try:
                # Test if this Python has tkinter
                result = subprocess.run([
                    python_path, '-c', 'import tkinter; print("tkinter available")'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and "tkinter available" in result.stdout:
                    print(f"✅ Found Python with tkinter: {python_path}")
                    return python_path
                else:
                    print(f"❌ Python at {python_path} doesn't have tkinter")
            except Exception as e:
                print(f"❌ Error testing {python_path}: {e}")
    
    return None

def install_dependencies_system_python(python_path):
    """Install dependencies using system Python"""
    dependencies = [
        'pyinstaller',
        'customtkinter',
        'pillow',
        'keyring'
    ]
    
    for dep in dependencies:
        try:
            print(f"📦 Installing {dep}...")
            result = subprocess.run([
                python_path, '-m', 'pip', 'install', '--user', dep
            ], check=True, capture_output=True, text=True)
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            return False
    
    return True

def clean_build():
    """Clean previous build artifacts"""
    dirs_to_clean = ['build', 'dist']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 Cleaned {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"🧹 Cleaned {file}")

def build_with_system_python():
    """Build the app using system Python"""
    
    print("🚀 Building VS Mac Tools v2 with system Python...")
    
    # Find system Python with tkinter
    python_path = find_system_python()
    if not python_path:
        print("❌ Could not find system Python with tkinter support")
        print("💡 Try installing Python from python.org or using Homebrew:")
        print("   brew install python-tk")
        return False
    
    # Install dependencies
    print("📦 Installing dependencies...")
    if not install_dependencies_system_python(python_path):
        print("❌ Failed to install dependencies")
        return False
    
    # Clean previous builds
    clean_build()
    
    # Build command using system Python
    cmd = [
        python_path, '-m', 'PyInstaller',
        '--onedir',
        '--windowed',
        '--name=VS Mac Tools v2',
        '--clean',
        '--noconfirm',
        '--icon=img/vs_mac_tool_v2.icns',
        '--add-data=img:img',
        '--add-data=xml:xml',
        '--add-data=password_dialog.py:.',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.simpledialog',
        '--hidden-import=_tkinter',
        '--hidden-import=customtkinter',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL.ImageTk',
        '--hidden-import=keyring',
        '--collect-data=customtkinter',
        '--collect-data=tkinter',
        '--osx-bundle-identifier=com.vonzki.vsmactools',
        'vs_mac_tools_v2.py'
    ]
    
    print("🔨 Running PyInstaller with system Python...")
    print(f"Using Python: {python_path}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, text=True)
        print("✅ PyInstaller completed successfully")
        
        # Check if app was created
        app_path = "dist/VS Mac Tools v2.app"
        if os.path.exists(app_path):
            print(f"✅ App created successfully: {app_path}")
            
            # Set executable permissions
            executable_path = f"{app_path}/Contents/MacOS/VS Mac Tools v2"
            if os.path.exists(executable_path):
                os.chmod(executable_path, 0o755)
                print("✅ Set executable permissions")
            
            # Check app size
            size_result = subprocess.run(['du', '-sh', app_path], capture_output=True, text=True)
            if size_result.returncode == 0:
                size = size_result.stdout.strip().split()[0]
                print(f"📦 App size: {size}")
            
            return True
        else:
            print("❌ App was not created")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller failed: {e}")
        return False

def test_app():
    """Test the built app"""
    app_path = "dist/VS Mac Tools v2.app"
    if os.path.exists(app_path):
        print("🧪 Testing app...")
        try:
            subprocess.run(['open', app_path], check=True)
            print("✅ App launched successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to launch app: {e}")
            return False
    else:
        print("❌ App not found for testing")
        return False

def main():
    """Main build process"""
    print("=" * 60)
    print("VS Mac Tools v2 - System Python Build")
    print("=" * 60)
    
    # Check if we're on macOS
    if sys.platform != 'darwin':
        print("❌ This script must be run on macOS")
        sys.exit(1)
    
    # Check if main script exists
    if not os.path.exists('vs_mac_tools_v2.py'):
        print("❌ vs_mac_tools_v2.py not found")
        sys.exit(1)
    
    # Build the app
    if build_with_system_python():
        print("\n" + "=" * 60)
        print("✅ BUILD SUCCESSFUL!")
        print("=" * 60)
        print(f"📱 App location: dist/VS Mac Tools v2.app")
        print("🚀 You can now test and distribute this app")
        
        # Ask if user wants to test
        response = input("\n🧪 Do you want to test the app now? (y/n): ")
        if response.lower() in ['y', 'yes']:
            test_app()
    else:
        print("\n" + "=" * 60)
        print("❌ BUILD FAILED!")
        print("=" * 60)
        print("\n💡 Try these solutions:")
        print("1. Install Python with tkinter: brew install python-tk")
        print("2. Install Python from python.org")
        print("3. Check that tkinter is available: python3 -c 'import tkinter'")
        sys.exit(1)

if __name__ == "__main__":
    main()
