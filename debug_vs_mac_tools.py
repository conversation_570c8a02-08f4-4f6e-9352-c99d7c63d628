
import sys
import traceback
import os

def debug_main():
    try:
        print("Debug: Starting VS Mac Tools v2...")
        print(f"Debug: Python version: {sys.version}")
        print(f"Debug: Current working directory: {os.getcwd()}")
        print(f"Debug: Python path: {sys.path}")
        
        # Test imports one by one
        print("Debug: Testing imports...")
        
        try:
            import tkinter
            print("Debug: ✅ tkinter imported successfully")
        except Exception as e:
            print(f"Debug: ❌ tkinter import failed: {e}")
            
        try:
            import customtkinter
            print("Debug: ✅ customtkinter imported successfully")
        except Exception as e:
            print(f"Debug: ❌ customtkinter import failed: {e}")
            
        try:
            import PIL
            print("Debug: ✅ PIL imported successfully")
        except Exception as e:
            print(f"Debug: ❌ PIL import failed: {e}")
            
        try:
            import keyring
            print("Debug: ✅ keyring imported successfully")
        except Exception as e:
            print(f"Debug: ❌ keyring import failed: {e}")
        
        # Try to import the main module
        print("Debug: Importing main application...")
        import vs_mac_tools_v2
        print("Debug: ✅ Main module imported successfully")
        
        # Try to create the app
        print("Debug: Creating application instance...")
        app = vs_mac_tools_v2.VSMacToolsApp()
        print("Debug: ✅ Application instance created")
        
        print("Debug: Starting main loop...")
        app.mainloop()
        
    except Exception as e:
        print(f"Debug: ❌ Error occurred: {e}")
        print(f"Debug: Traceback:")
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    debug_main()
