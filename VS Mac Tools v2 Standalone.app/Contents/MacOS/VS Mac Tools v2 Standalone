#!/bin/bash
# VS Mac Tools v2 Standalone Launcher

# Get the directory of this script
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_DIR="$DIR/../Resources"

# Set environment variables
export TK_SILENCE_DEPRECATION=1
export PYTHONPATH="$RESOURCES_DIR/site-packages:$RESOURCES_DIR:$PYTHONPATH"

# Change to resources directory
cd "$RESOURCES_DIR"

# Check if system Python exists
if ! command -v python3 &> /dev/null; then
    osascript -e 'display alert "Python Required" message "This app requires Python 3 to be installed on your system. Please install Python 3 and try again."'
    exit 1
fi

# Check if required modules are available
python3 -c "import customtkinter, PIL, keyring" 2>/dev/null
if [ $? -ne 0 ]; then
    # Try to install required packages
    osascript -e 'display alert "Installing Dependencies" message "Installing required Python packages. This may take a moment..."'
    python3 -m pip install --user customtkinter pillow keyring
fi

# Launch the app
python3 vs_mac_tools_v2.py

# Only show error if it's a real error (not normal exit or KeyboardInterrupt)
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ] && [ $EXIT_CODE -ne 130 ]; then
    osascript -e 'display alert "Error" message "VS Mac Tools v2 encountered an error. Please check your Python installation."'
fi
