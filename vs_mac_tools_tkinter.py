#!/usr/bin/env python3

import os
os.environ['TK_SILENCE_DEPRECATION'] = '1'

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import subprocess
import threading
import time
import csv
import queue
from datetime import datetime

class VSMacToolsApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("VS Mac Tools v2 - Standard Tkinter")
        self.root.geometry("1000x700")
        
        # Configure style
        self.root.configure(bg='#2b2b2b')
        
        # Create main frame
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left frame for controls
        left_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        left_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # Process Steps
        steps_label = tk.Label(left_frame, text="Process Steps", 
                              font=('Arial', 14, 'bold'), 
                              bg='#3b3b3b', fg='white')
        steps_label.pack(pady=10)
        
        # Checkboxes for process steps
        self.checkboxes = []
        self.checkbox_vars = []
        
        steps = [
            "Check Time on time.apple.com",
            "Check Apple Server Connection", 
            "Check ADE/DEP Enrollment",
            "Check MDM Enrollment Status",
            "Check Installed Profiles",
            "Show Device Enrollment Log",
            "Remove Paired Bluetooth Devices"
        ]
        
        for step in steps:
            var = tk.BooleanVar()
            cb = tk.Checkbutton(left_frame, text=step, variable=var,
                              bg='#3b3b3b', fg='white', selectcolor='#3b3b3b',
                              font=('Arial', 10))
            cb.pack(anchor='w', padx=10, pady=2)
            self.checkboxes.append(cb)
            self.checkbox_vars.append(var)
        
        # Execute button
        execute_btn = tk.Button(left_frame, text="Execute All",
                               command=self.execute_all,
                               bg='#007AFF', fg='white',
                               font=('Arial', 12, 'bold'),
                               relief='flat', padx=20, pady=5)
        execute_btn.pack(pady=20)
        
        # System Info
        info_label = tk.Label(left_frame, text="System Information",
                             font=('Arial', 14, 'bold'),
                             bg='#3b3b3b', fg='white')
        info_label.pack(pady=(20, 10))
        
        # System info display
        self.info_text = tk.Text(left_frame, height=8, width=30,
                                bg='#2b2b2b', fg='white',
                                font=('Monaco', 10))
        self.info_text.pack(padx=10, pady=5)
        
        # Shortcuts
        shortcuts_label = tk.Label(left_frame, text="Shortcuts",
                                  font=('Arial', 14, 'bold'),
                                  bg='#3b3b3b', fg='white')
        shortcuts_label.pack(pady=(20, 10))
        
        shortcuts = [
            ("Bluetooth", self.open_bluetooth),
            ("FindMy", self.open_findmy),
            ("System Preferences", self.open_system_prefs),
            ("Exit", self.exit_app)
        ]
        
        for name, command in shortcuts:
            btn = tk.Button(left_frame, text=name, command=command,
                           bg='#4b4b4b', fg='white',
                           font=('Arial', 10),
                           relief='flat', width=20)
            btn.pack(pady=2, padx=10)
        
        # Right frame for logs
        right_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        right_frame.pack(side='right', fill='both', expand=True)
        
        # Process logs
        logs_label = tk.Label(right_frame, text="Process Logs",
                             font=('Arial', 14, 'bold'),
                             bg='#3b3b3b', fg='white')
        logs_label.pack(pady=10)
        
        # Log text area
        self.log_text = tk.Text(right_frame, bg='#1a1a1a', fg='white',
                               font=('Monaco', 11), wrap='word')
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scrollbar for logs
        scrollbar = tk.Scrollbar(self.log_text)
        scrollbar.pack(side='right', fill='y')
        self.log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.log_text.yview)
        
        # Status bar
        status_frame = tk.Frame(self.root, bg='#3b3b3b', relief='sunken', bd=1)
        status_frame.pack(side='bottom', fill='x')
        
        self.status_label = tk.Label(status_frame, text="Ready",
                                    bg='#3b3b3b', fg='white',
                                    font=('Arial', 10))
        self.status_label.pack(side='left', padx=10, pady=2)
        
        # Date/time
        self.datetime_label = tk.Label(status_frame, text="",
                                      bg='#3b3b3b', fg='#00FF00',
                                      font=('Monaco', 10))
        self.datetime_label.pack(side='right', padx=10, pady=2)
        
        # Initialize
        self.result_queue = queue.Queue()
        self.update_datetime()
        self.update_system_info()
        self.log("VS Mac Tools v2 initialized successfully!")
        
        # Start periodic updates
        self.root.after(1000, self.update_datetime)
        self.root.after(100, self.check_queue)
    
    def log(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert('end', f"[{timestamp}] {message}\n")
        self.log_text.see('end')
        self.root.update()
    
    def update_datetime(self):
        """Update date/time display"""
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.config(text=now)
        self.root.after(1000, self.update_datetime)
    
    def update_system_info(self):
        """Update system information"""
        try:
            # Get system info
            result = subprocess.run(['system_profiler', 'SPHardwareDataType'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                info_lines = []
                for line in lines:
                    if 'Model Identifier:' in line or 'Chip:' in line or 'Memory:' in line:
                        info_lines.append(line.strip())
                
                self.info_text.delete('1.0', 'end')
                self.info_text.insert('1.0', '\n'.join(info_lines))
        except Exception as e:
            self.log(f"Error getting system info: {e}")
    
    def check_queue(self):
        """Check for results from background threads"""
        try:
            while True:
                message = self.result_queue.get_nowait()
                self.log(message)
        except queue.Empty:
            pass
        self.root.after(100, self.check_queue)
    
    def execute_all(self):
        """Execute all process steps"""
        self.log("Starting process execution...")
        
        # Simple test commands
        commands = [
            ("Checking time...", "date"),
            ("Checking network...", "ping -c 1 *******"),
            ("Checking system...", "uname -a")
        ]
        
        for description, command in commands:
            self.log(description)
            try:
                result = subprocess.run(command.split(), 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.log(f"✅ {description} - Success")
                else:
                    self.log(f"❌ {description} - Failed")
            except Exception as e:
                self.log(f"❌ {description} - Error: {e}")
    
    def open_bluetooth(self):
        self.log("Opening Bluetooth preferences...")
        subprocess.run(['open', '/System/Library/PreferencePanes/Bluetooth.prefPane'])
    
    def open_findmy(self):
        self.log("Opening FindMy...")
        subprocess.run(['open', '-a', 'FindMy'])
    
    def open_system_prefs(self):
        self.log("Opening System Preferences...")
        subprocess.run(['open', '/Applications/System Preferences.app'])
    
    def exit_app(self):
        self.log("Exiting application...")
        self.root.quit()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🚀 Starting VS Mac Tools v2 (Standard Tkinter)...")
    app = VSMacToolsApp()
    app.run()
    print("👋 Application closed")
